# PumpFun Token Data Guide

This document explains all the data fields available from PumpFun tokens and how they are displayed in the monitoring system.

## Available Data Fields

### Basic Token Information
- **name**: The token's display name (e.g., "NOTH<PERSON><PERSON><PERSON>", "Memedic")
- **symbol**: Trading symbol/ticker (e.g., "NOTH", "MEME")
- **mint**: The token's unique mint address on Solana blockchain
- **timestamp**: When the token was detected by our monitor
- **signature**: Transaction signature of the token creation

### Social Media & Links
- **twitter**: Link to X/Twitter profile or community
- **telegram**: Telegram group/channel invite link
- **website**: Official project website URL
- **description**: Project description text (can be empty or detailed)

### Token Image
- **image**: Token logo/icon URL (automatically resolved from IPFS)
- **uri**: Original metadata URI (IPFS or HTTP link)

### Technical Details
- **totalSupply**: Always 1,000,000,000 (1 billion) for PumpFun tokens
- **decimals**: Always 6 for PumpFun tokens
- **creator**: Wallet address that created the token
- **bondingCurve**: Address of the bonding curve contract
- **showName**: Boolean flag for display preferences
- **createdOn**: Always "https://pump.fun" for authentic tokens

### Additional Platform Data
- **Market Cap**: Starts at ~$7,000 (3 SOL initial liquidity)
- **Graduation**: Tokens graduate to Raydium at $69,000 market cap
- **Update Authority**: Set to PumpFun program (immutable metadata)

## Frontend Display Features

### Main Token Card Shows:
1. **Token Image** - With fallback gradient placeholder
2. **Name & Symbol** - Prominently displayed
3. **Description** - Truncated to 2 lines if too long
4. **Mint Address** - Clickable link to Solscan
5. **Transaction Link** - View creation transaction
6. **Supply & Decimals** - Standard token info
7. **Timestamp** - When token was created

### Social Links Section:
- **Twitter/X** - With bird icon
- **Telegram** - With Telegram icon
- **Website** - With globe icon
- **Pump.fun** - Direct link to token page

### Explorer Links:
- **Solscan Token Page**: `https://solscan.io/token/[mint]`
- **Transaction Details**: `https://solscan.io/tx/[signature]`
- **Pump.fun Page**: `https://pump.fun/[mint]`

## Data Quality Notes

### Common Issues:
1. **Missing Social Links** - Many tokens don't provide social media
2. **Empty Descriptions** - Some creators skip descriptions
3. **Image Loading** - IPFS images may load slowly
4. **Incorrect URLs** - Sometimes Twitter links in Telegram field

### Data Validation:
- All PumpFun tokens have exactly 1B supply
- Mint addresses always end with "pump"
- Metadata becomes immutable after creation
- Social links cannot be updated later

## Real-World Examples

### Complete Token (NOTHEORY):
```json
{
  "name": "NOTHEORY",
  "symbol": "NOTH",
  "description": "The Official token of NOTHEORY Perfume...",
  "twitter": "https://x.com/notheorycoin",
  "telegram": "https://t.me/notheorycoin",
  "website": "https://notheory.id/",
  "image": "https://ipfs.io/ipfs/..."
}
```

### Minimal Token:
```json
{
  "name": "Test Token",
  "symbol": "TEST",
  "description": "",
  "image": "https://ipfs.io/ipfs/..."
}
```

## Usage Tips

1. **Quick Identification**: Look for tokens with complete social profiles
2. **Community Strength**: Active Twitter/Telegram often indicates engagement
3. **Professional Tokens**: Usually have websites and descriptions
4. **Meme Tokens**: Often have funny names/images but minimal info
5. **Scam Detection**: Be wary of tokens copying popular names

## Technical Implementation

The monitor extracts this data through:
1. Real-time blockchain log monitoring
2. Base64 decoding of program data
3. IPFS metadata resolution
4. Transaction analysis for addresses

All data is fetched natively without third-party APIs, ensuring accuracy and real-time updates.