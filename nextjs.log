
> pulse@0.1.0 dev
> next dev

   ▲ Next.js 15.3.5
   - Local:        http://localhost:3000
   - Network:      http://***************:3000
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 2s
 ○ Compiling / ...
 ✓ Compiled / in 6.2s (721 modules)
 GET / 200 in 6608ms
 ○ Compiling /api/pump-monitor ...
 ✓ Compiled /api/pump-monitor in 4.3s (1354 modules)
Starting background updater for token metrics
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
No tokens in cache to update
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 GET /api/pump-monitor 200 in 10125ms
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 315ms (324 modules)
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 1401ms (1040 modules)
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 536ms (1040 modules)
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
No tokens in cache to update
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled / in 162ms (380 modules)
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 GET / 200 in 399ms
 ○ Compiling /favicon.ico ...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled /api/pump-monitor in 1216ms (396 modules)
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled (1044 modules)
Starting background updater for token metrics
 GET /favicon.ico 200 in 2467ms
Redis Client Error ECONNREFUSED
Running without Redis cache - tokens will not persist
Redis Client Error ECONNREFUSED
Redis Client Error ECONNREFUSED
No tokens in cache to update
Failed to connect to Redis: [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error decoding program data: RangeError: The value of "offset" is out of range. It must be >= 0 and <= 221. Received 3661543545
    at decodeProgramData (src/app/api/pump-monitor/route.ts:67:27)
    at eval (src/app/api/pump-monitor/route.ts:210:36)
    at Set.forEach (<anonymous>)
  65 |     
  66 |     // Read string length and content for symbol
> 67 |     const symbolLen = data.readUInt32LE(offset);
     |                           ^
  68 |     offset += 4;
  69 |     const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
  70 |     offset += symbolLen; {
  code: 'ERR_OUT_OF_RANGE'
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching metadata from https://cloudflare-ipfs.com/ipfs/QmbtPujgt4McDok3ChwYKNncrbEdMuNBSmTT9KGUW4jTcB : TypeError: fetch failed
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:118:23)
    at async eval (src/app/api/pump-monitor/route.ts:278:43)
  116 |     
  117 |     try {
> 118 |       const response = await fetch(httpUri, { 
      |                       ^
  119 |         signal: controller.signal,
  120 |         headers: {
  121 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  [cause]: [Error: getaddrinfo ENOTFOUND cloudflare-ipfs.com] {
    errno: -3007,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'cloudflare-ipfs.com'
  }
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Cimmy',
  symbol: 'CIMMY',
  mint: 'CcuNCyhgFePuWxsNn1fQ7hrr9eVrGUS7SaYBEkTnrcPq',
  marketCap: '$178.47',
  hasImage: false
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
No tokens in cache to update
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'I identify as a BONK',
  symbol: 'BONK',
  mint: '6rRYsoGgVyXbQw7D2Z9KmYmxSpJiixJudC2xiC4Wbonk',
  marketCap: '$178.55',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'arrest o clock',
  symbol: '0000',
  mint: 'FzaG47kv1F4o9RF8MdmtfDgyvgx76iiQaony7uZdpump',
  marketCap: '$0.00',
  hasImage: true
}
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'young bonk and rich',
  symbol: 'ybr',
  mint: 'Ebqsi9EGBTWE7gH4uwAsy1Fx7LnCJfidS9YQsCripump',
  marketCap: '$694506.96',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Freestyle On Chain',
  symbol: 'RAP',
  mint: 'D2W2YLFg1w6epQ5ndWgAinARuye7ZcquVqrMoywzpump',
  marketCap: '$649225.27',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 925ms (1368 modules)
 GET / 200 in 132ms
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'labubu',
  symbol: 'LABU',
  mint: '7DbSSEs8Y5EjyPkkHeA1YLvwUb5DTGRxk92cNzzTpump',
  marketCap: '$680193.75',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 713ms (1368 modules)
 GET / 200 in 243ms
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:58:24)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Updating metrics for 6 tokens
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for D2W2YLFg1w6epQ5ndWgAinARuye7ZcquVqrMoywzpump in memory cache
Updated RAP: $649485.01 MC
Updated metrics for 7DbSSEs8Y5EjyPkkHeA1YLvwUb5DTGRxk92cNzzTpump in memory cache
Updated LABU: $659937.94 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for Ebqsi9EGBTWE7gH4uwAsy1Fx7LnCJfidS9YQsCripump in memory cache
Updated ybr: $658060.93 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for CcuNCyhgFePuWxsNn1fQ7hrr9eVrGUS7SaYBEkTnrcPq in memory cache
Updated CIMMY: $117.20 MC
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for 6rRYsoGgVyXbQw7D2Z9KmYmxSpJiixJudC2xiC4Wbonk in memory cache
Updated BONK: $117.20 MC
Finished updating token metrics
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 247ms (704 modules)
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 GET / 200 in 168ms
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error decoding program data: RangeError: The value of "offset" is out of range. It must be >= 0 and <= 221. Received 2116925883
    at decodeProgramData (src/app/api/pump-monitor/route.ts:67:27)
    at eval (src/app/api/pump-monitor/route.ts:210:36)
    at Set.forEach (<anonymous>)
  65 |     
  66 |     // Read string length and content for symbol
> 67 |     const symbolLen = data.readUInt32LE(offset);
     |                           ^
  68 |     offset += 4;
  69 |     const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
  70 |     offset += symbolLen; {
  code: 'ERR_OUT_OF_RANGE'
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 410ms (704 modules)
 GET / 200 in 52ms
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Excessive Gains Only',
  symbol: 'EGO',
  mint: 'AoZt7rFdXZQrUxath6b5qjmTeNTsKhfvDgobKKnZpump',
  marketCap: '$701613.26',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:58:24)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
No tokens in cache to update
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error decoding program data: RangeError: The value of "offset" is out of range. It must be >= 0 and <= 221. Received 1507908087
    at decodeProgramData (src/app/api/pump-monitor/route.ts:67:27)
    at eval (src/app/api/pump-monitor/route.ts:210:36)
    at Set.forEach (<anonymous>)
  65 |     
  66 |     // Read string length and content for symbol
> 67 |     const symbolLen = data.readUInt32LE(offset);
     |                           ^
  68 |     offset += 4;
  69 |     const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
  70 |     offset += symbolLen; {
  code: 'ERR_OUT_OF_RANGE'
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error decoding program data: RangeError: The value of "offset" is out of range. It must be >= 0 and <= 221. Received 2116925883
    at decodeProgramData (src/app/api/pump-monitor/route.ts:67:27)
    at eval (src/app/api/pump-monitor/route.ts:210:36)
    at Set.forEach (<anonymous>)
  65 |     
  66 |     // Read string length and content for symbol
> 67 |     const symbolLen = data.readUInt32LE(offset);
     |                           ^
  68 |     offset += 4;
  69 |     const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
  70 |     offset += symbolLen; {
  code: 'ERR_OUT_OF_RANGE'
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'What’s the time?',
  symbol: '0000',
  mint: 'EpuTD5NqW271komCL686GDRDRpoEkknMzbwMNQs2pump',
  marketCap: '$1018295.00',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updating metrics for 8 tokens
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for EpuTD5NqW271komCL686GDRDRpoEkknMzbwMNQs2pump in memory cache
Updated 0000: $1021558.91 MC
Updated metrics for 7DbSSEs8Y5EjyPkkHeA1YLvwUb5DTGRxk92cNzzTpump in memory cache
Updated LABU: $1009330.22 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for AoZt7rFdXZQrUxath6b5qjmTeNTsKhfvDgobKKnZpump in memory cache
Updated EGO: $1106840.22 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for D2W2YLFg1w6epQ5ndWgAinARuye7ZcquVqrMoywzpump in memory cache
Updated RAP: $989393.49 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for Ebqsi9EGBTWE7gH4uwAsy1Fx7LnCJfidS9YQsCripump in memory cache
Updated ybr: $995754.38 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for CcuNCyhgFePuWxsNn1fQ7hrr9eVrGUS7SaYBEkTnrcPq in memory cache
Updated CIMMY: $178.54 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Mbpepe',
  symbol: 'Mbpepe',
  mint: '3iugg1fWyBsjkaz7tq4kqzJMQop6JpBzgSCsQ7gopump',
  marketCap: '$1042290.46',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error decoding program data: RangeError: The value of "offset" is out of range. It must be >= 0 and <= 221. Received 3284752552
    at decodeProgramData (src/app/api/pump-monitor/route.ts:67:27)
    at eval (src/app/api/pump-monitor/route.ts:210:36)
    at Set.forEach (<anonymous>)
  65 |     
  66 |     // Read string length and content for symbol
> 67 |     const symbolLen = data.readUInt32LE(offset);
     |                           ^
  68 |     offset += 4;
  69 |     const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
  70 |     offset += symbolLen; {
  code: 'ERR_OUT_OF_RANGE'
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
No tokens in cache to update
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for 6rRYsoGgVyXbQw7D2Z9KmYmxSpJiixJudC2xiC4Wbonk in memory cache
Updated BONK: $178.54 MC
Finished updating token metrics
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: '100% real',
  symbol: '100%REAL',
  mint: '5NhPouPn3LvSTaJqVcxo3fxxiqwBQ1CJFyGzKS4fpUpJ',
  marketCap: '$0.00',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'SHIT COIN',
  symbol: 'SHIT COIN',
  mint: '3uszyrR9XLR5rX4F61ySuP8vQAkZa6DGSMcxCQXepump',
  marketCap: '$992878.77',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updating metrics for 10 tokens
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'MINION MUSK',
  symbol: 'MM',
  mint: 'BLUW8QVLd82YK4DtTNAuGeDp35Ys9GRWZtRj2KyApump',
  marketCap: '$0.00',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error getting token activity: Error [SolanaJSONRPCError]: failed to get signatures for address: Failed to query long-term storage; please try again
    at async getTokenActivity (src/lib/market-data.ts:115:23)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  113 |   
  114 |   try {
> 115 |     const signatures = await connection.getSignaturesForAddress(
      |                       ^
  116 |       new PublicKey(bondingCurve),
  117 |       { limit }
  118 |     ); {
  code: -32019,
  data: undefined
}
Updated metrics for 3uszyrR9XLR5rX4F61ySuP8vQAkZa6DGSMcxCQXepump in memory cache
Updated SHIT COIN: $992878.77 MC
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Fiat Is Hopeless So Yes',
  symbol: 'FIATHSY',
  mint: 'Azasi26auUVCRAACyezFRh5noAS8TZjwTgdbt29jhSx6',
  marketCap: '$117.20',
  hasImage: true
}
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'o’clock',
  symbol: '00:00',
  mint: '2a93Qd3G2Yp6bWAKKwUHvTTJd6grWyXeuyXJhCnFpump',
  marketCap: '$758585.03',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for 3iugg1fWyBsjkaz7tq4kqzJMQop6JpBzgSCsQ7gopump in memory cache
Updated Mbpepe: $1062827.06 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'awdad',
  symbol: 'adadad',
  mint: '21rHwWdaYYAx6XQ7kXx3MEmY5u9HLZkhMgUcxbi75wUv',
  marketCap: '$117.20',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated metrics for AoZt7rFdXZQrUxath6b5qjmTeNTsKhfvDgobKKnZpump in memory cache
Updated EGO: $997585.61 MC
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error decoding program data: RangeError: The value of "offset" is out of range. It must be >= 0 and <= 52. Received 3059774092
    at decodeProgramData (src/app/api/pump-monitor/route.ts:67:27)
    at eval (src/app/api/pump-monitor/route.ts:210:36)
    at Set.forEach (<anonymous>)
  65 |     
  66 |     // Read string length and content for symbol
> 67 |     const symbolLen = data.readUInt32LE(offset);
     |                           ^
  68 |     offset += 4;
  69 |     const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
  70 |     offset += symbolLen; {
  code: 'ERR_OUT_OF_RANGE'
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for 7DbSSEs8Y5EjyPkkHeA1YLvwUb5DTGRxk92cNzzTpump in memory cache
Updated LABU: $1009992.82 MC
Updated metrics for EpuTD5NqW271komCL686GDRDRpoEkknMzbwMNQs2pump in memory cache
Updated 0000: $1026787.12 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated RAP: $990043.00 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated ybr: $996408.06 MC
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'GREEN CHIP',
  symbol: 'GREEN CHIP',
  mint: 'HvVqUMCP7xH9sxgeUg61TDBoE9Htw6LACJRj4NnGpump',
  marketCap: '$884676.83',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated BONK: $178.65 MC
Finished updating token metrics
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:58:24)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
No tokens in cache to update
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Baby Nigga Bonk',
  symbol: 'BNB',
  mint: '2DHNcWxb2JXnkKsWtrWrJoCnvEyQR9ciAoRVin87pump',
  marketCap: '$774310.76',
  hasImage: true
}
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'MMAOTAI',
  symbol: 'mt',
  mint: '8yNuqCRZwd9piqwo45KcsSdwKaW2DxMkpbbNKTBfPUMP',
  marketCap: '$117.20',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching metadata from https://cloudflare-ipfs.com/ipfs/QmP9EJThvfEHDAnHXDCc7r4VtAjsaacQHGy5GuGK5uMFiN : TypeError: fetch failed
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:118:23)
    at async eval (src/app/api/pump-monitor/route.ts:278:43)
  116 |     
  117 |     try {
> 118 |       const response = await fetch(httpUri, { 
      |                       ^
  119 |         signal: controller.signal,
  120 |         headers: {
  121 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  [cause]: [Error: getaddrinfo ENOTFOUND cloudflare-ipfs.com] {
    errno: -3007,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'cloudflare-ipfs.com'
  }
}
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Cash Party',
  symbol: 'CASH',
  mint: 'SKr3VjhsrpBsnR3ax86hyqXTsJoqBJRWRLEzv4ctJND',
  marketCap: '$117.20',
  hasImage: false
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'ЛУНА',
  symbol: 'ЛУНА',
  mint: 'A4tXwRCnAcPXz9zNztHxhG64aYgunDdTiwRyUpHipump',
  marketCap: '$671071.18',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error decoding program data: RangeError: The value of "offset" is out of range. It must be >= 0 and <= 221. Received 381554937
    at decodeProgramData (src/app/api/pump-monitor/route.ts:67:27)
    at eval (src/app/api/pump-monitor/route.ts:210:36)
    at Set.forEach (<anonymous>)
  65 |     
  66 |     // Read string length and content for symbol
> 67 |     const symbolLen = data.readUInt32LE(offset);
     |                           ^
  68 |     offset += 4;
  69 |     const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
  70 |     offset += symbolLen; {
  code: 'ERR_OUT_OF_RANGE'
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: "Arrest O'Clock",
  symbol: 'ARREST',
  mint: '9Q4bqxNoEYkMQ5ftDmnEYQTgK9h1wJxdPQJSxqhbean',
  marketCap: '$117.20',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:58:24)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Updating metrics for 10 tokens
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for SKr3VjhsrpBsnR3ax86hyqXTsJoqBJRWRLEzv4ctJND in memory cache
Updated CASH: $117.20 MC
Error decoding program data: RangeError: The value of "offset" is out of range. It must be >= 0 and <= 221. Received 1407823149
    at decodeProgramData (src/app/api/pump-monitor/route.ts:67:27)
    at eval (src/app/api/pump-monitor/route.ts:210:36)
    at Set.forEach (<anonymous>)
  65 |     
  66 |     // Read string length and content for symbol
> 67 |     const symbolLen = data.readUInt32LE(offset);
     |                           ^
  68 |     offset += 4;
  69 |     const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
  70 |     offset += symbolLen; {
  code: 'ERR_OUT_OF_RANGE'
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for 9Q4bqxNoEYkMQ5ftDmnEYQTgK9h1wJxdPQJSxqhbean in memory cache
Updated ARREST: $117.20 MC
Updated metrics for A4tXwRCnAcPXz9zNztHxhG64aYgunDdTiwRyUpHipump in memory cache
Updated ЛУНА: $654760.57 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error decoding program data: RangeError: The value of "offset" is out of range. It must be >= 0 and <= 221. Received 3284752552
    at decodeProgramData (src/app/api/pump-monitor/route.ts:67:27)
    at eval (src/app/api/pump-monitor/route.ts:210:36)
    at Set.forEach (<anonymous>)
  65 |     
  66 |     // Read string length and content for symbol
> 67 |     const symbolLen = data.readUInt32LE(offset);
     |                           ^
  68 |     offset += 4;
  69 |     const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
  70 |     offset += symbolLen; {
  code: 'ERR_OUT_OF_RANGE'
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for 8yNuqCRZwd9piqwo45KcsSdwKaW2DxMkpbbNKTBfPUMP in memory cache
Updated mt: $117.20 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching metadata from https://ipfs.io/ipfs/bafkreifwkhkvgva4nnedhgrfay3ffcq7wsegr2vk36i4m22g5ccapdbdum : Error [AbortError]: This operation was aborted
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:118:23)
    at async eval (src/app/api/pump-monitor/route.ts:278:43)
  116 |     
  117 |     try {
> 118 |       const response = await fetch(httpUri, { 
      |                       ^
  119 |         signal: controller.signal,
  120 |         headers: {
  121 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  code: 20,
  INDEX_SIZE_ERR: 1,
  DOMSTRING_SIZE_ERR: 2,
  HIERARCHY_REQUEST_ERR: 3,
  WRONG_DOCUMENT_ERR: 4,
  INVALID_CHARACTER_ERR: 5,
  NO_DATA_ALLOWED_ERR: 6,
  NO_MODIFICATION_ALLOWED_ERR: 7,
  NOT_FOUND_ERR: 8,
  NOT_SUPPORTED_ERR: 9,
  INUSE_ATTRIBUTE_ERR: 10,
  INVALID_STATE_ERR: 11,
  SYNTAX_ERR: 12,
  INVALID_MODIFICATION_ERR: 13,
  NAMESPACE_ERR: 14,
  INVALID_ACCESS_ERR: 15,
  VALIDATION_ERR: 16,
  TYPE_MISMATCH_ERR: 17,
  SECURITY_ERR: 18,
  NETWORK_ERR: 19,
  ABORT_ERR: 20,
  URL_MISMATCH_ERR: 21,
  QUOTA_EXCEEDED_ERR: 22,
  TIMEOUT_ERR: 23,
  INVALID_NODE_TYPE_ERR: 24,
  DATA_CLONE_ERR: 25
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Pawlien ',
  symbol: 'Pawlien',
  mint: '9hUjaf9R2kXaLN4Tjf8WR5CuaKXwNvcS8k7EzYrMpump',
  marketCap: '$1037095.73',
  hasImage: false
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for 2DHNcWxb2JXnkKsWtrWrJoCnvEyQR9ciAoRVin87pump in memory cache
Updated BNB: $658165.53 MC
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Poop WIF',
  symbol: 'pooopwif',
  mint: 'FNKXfrTzGXq2uiM1Q7r8ftXqGGkcoQXeMKQ2nHeCpump',
  marketCap: '$167.27',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for HvVqUMCP7xH9sxgeUg61TDBoE9Htw6LACJRj4NnGpump in memory cache
Updated GREEN CHIP: $653448.88 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated FIATHSY: $117.20 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'goodmorning from whashywash',
  symbol: 'goodmornin',
  mint: 'BUYxwab91oeWaXhuf29tPyevoxBsoHrQWTRBRn7x3r8G',
  marketCap: '$178.65',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updated 00:00: $767448.49 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
No tokens in cache to update
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for 21rHwWdaYYAx6XQ7kXx3MEmY5u9HLZkhMgUcxbi75wUv in memory cache
Updated adadad: $117.20 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Pussy On Fire',
  symbol: 'PUSSYFIRE ',
  mint: 'F2EmecMzrYFQVrVKuf98zonYUgekeYEbtmaN9Jm7pump',
  marketCap: '$989939.76',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Finished updating token metrics
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'goodmorning TO whashywash',
  symbol: 'goodmornin',
  mint: 'H6oqrwwXo115ameUvgtSFUWapHiuSjCH5L49sHw3yyE',
  marketCap: '$117.20',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 425ms (324 modules)
 GET / 200 in 71ms
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:58:24)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Updating metrics for 10 tokens
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'goodmorning from whashywash',
  symbol: 'goodmornin',
  mint: 'qExABszEm8dQZY9eqCLHXU98zA94z13kXP1oN47pump',
  marketCap: '$672815.30',
  hasImage: true
}
Updated metrics for F2EmecMzrYFQVrVKuf98zonYUgekeYEbtmaN9Jm7pump in memory cache
Updated PUSSYFIRE : $649763.68 MC
Updated metrics for H6oqrwwXo115ameUvgtSFUWapHiuSjCH5L49sHw3yyE in memory cache
Updated goodmornin: $117.20 MC
 ✓ Compiled in 424ms (324 modules)
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 GET / 200 in 22ms
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'I cant sell',
  symbol: 'FLOYD',
  mint: 'Dchhy3LsW6yS3xsv4vvn2CesbWFqD2yvRpexwXtKpump',
  marketCap: '$694670.39',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 173ms (324 modules)
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 GET / 200 in 35ms
Updated metrics for BUYxwab91oeWaXhuf29tPyevoxBsoHrQWTRBRn7x3r8G in memory cache
Updated goodmornin: $117.20 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 147ms (324 modules)
 GET / 200 in 18ms
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated metrics for 9Q4bqxNoEYkMQ5ftDmnEYQTgK9h1wJxdPQJSxqhbean in memory cache
Updated ARREST: $117.20 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
 ✓ Compiled in 135ms (324 modules)
 GET / 200 in 20ms
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for FNKXfrTzGXq2uiM1Q7r8ftXqGGkcoQXeMKQ2nHeCpump in memory cache
Updated pooopwif: $109.73 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for 9hUjaf9R2kXaLN4Tjf8WR5CuaKXwNvcS8k7EzYrMpump in memory cache
Updated Pawlien: $699473.50 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:58:24)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
No tokens in cache to update
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated mt: $117.20 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for SKr3VjhsrpBsnR3ax86hyqXTsJoqBJRWRLEzv4ctJND in memory cache
Updated CASH: $117.20 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
 ✓ Compiled in 177ms (324 modules)
 GET / 200 in 17ms
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for A4tXwRCnAcPXz9zNztHxhG64aYgunDdTiwRyUpHipump in memory cache
Updated ЛУНА: $654782.21 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated BNB: $653895.33 MC
Finished updating token metrics
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'trapezoid pepe',
  symbol: 'pepezoid',
  mint: '5SARcUwVkKLc4U8LjEneRe9AbC3YmApCdYZue5o5pump',
  marketCap: '$1059025.01',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updating metrics for 10 tokens
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'GREEN CHIP',
  symbol: 'GREEN CHIP',
  mint: '2tVaLMB37g8K7sq6mV9JgFrhNxoCSMAnhQLAgJgGpump',
  marketCap: '$1348689.83',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for 5SARcUwVkKLc4U8LjEneRe9AbC3YmApCdYZue5o5pump in memory cache
Updated pepezoid: $1059025.01 MC
Updated metrics for qExABszEm8dQZY9eqCLHXU98zA94z13kXP1oN47pump in memory cache
Updated goodmornin: $990336.90 MC
Updated metrics for Dchhy3LsW6yS3xsv4vvn2CesbWFqD2yvRpexwXtKpump in memory cache
Updated FLOYD: $990170.42 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
 ✓ Compiled in 196ms (324 modules)
 GET / 200 in 36ms
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated metrics for H6oqrwwXo115ameUvgtSFUWapHiuSjCH5L49sHw3yyE in memory cache
Updated goodmornin: $178.68 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated metrics for F2EmecMzrYFQVrVKuf98zonYUgekeYEbtmaN9Jm7pump in memory cache
Updated PUSSYFIRE : $997119.28 MC
Updated metrics for BUYxwab91oeWaXhuf29tPyevoxBsoHrQWTRBRn7x3r8G in memory cache
Updated goodmornin: $178.68 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error fetching metadata from https://ipfs.io/ipfs/bafkreia44gc4t56kp22nbmo2to4b5omdgjxdqurthptacg5y6uld7vftw4 : Error [AbortError]: This operation was aborted
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:118:23)
    at async eval (src/app/api/pump-monitor/route.ts:278:43)
  116 |     
  117 |     try {
> 118 |       const response = await fetch(httpUri, { 
      |                       ^
  119 |         signal: controller.signal,
  120 |         headers: {
  121 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  code: 20,
  INDEX_SIZE_ERR: 1,
  DOMSTRING_SIZE_ERR: 2,
  HIERARCHY_REQUEST_ERR: 3,
  WRONG_DOCUMENT_ERR: 4,
  INVALID_CHARACTER_ERR: 5,
  NO_DATA_ALLOWED_ERR: 6,
  NO_MODIFICATION_ALLOWED_ERR: 7,
  NOT_FOUND_ERR: 8,
  NOT_SUPPORTED_ERR: 9,
  INUSE_ATTRIBUTE_ERR: 10,
  INVALID_STATE_ERR: 11,
  SYNTAX_ERR: 12,
  INVALID_MODIFICATION_ERR: 13,
  NAMESPACE_ERR: 14,
  INVALID_ACCESS_ERR: 15,
  VALIDATION_ERR: 16,
  TYPE_MISMATCH_ERR: 17,
  SECURITY_ERR: 18,
  NETWORK_ERR: 19,
  ABORT_ERR: 20,
  URL_MISMATCH_ERR: 21,
  QUOTA_EXCEEDED_ERR: 22,
  TIMEOUT_ERR: 23,
  INVALID_NODE_TYPE_ERR: 24,
  DATA_CLONE_ERR: 25
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Push Up Memes Prices',
  symbol: 'PUMP',
  mint: 'ED8sYecVmKFvEA6HKnRZa7bops8UQ8L9A5s83S54pump',
  marketCap: '$1073927.74',
  hasImage: false
}
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
 ✓ Compiled in 1632ms (704 modules)
 GET / 200 in 105ms
Error fetching metadata from https://cloudflare-ipfs.com/ipfs/Qmc8V9xcqRW4xbCzzzjNjMyjHZbPnSUMWf1Dkw2GXBjLSu : TypeError: fetch failed
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:118:23)
    at async eval (src/app/api/pump-monitor/route.ts:278:43)
  116 |     
  117 |     try {
> 118 |       const response = await fetch(httpUri, { 
      |                       ^
  119 |         signal: controller.signal,
  120 |         headers: {
  121 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  [cause]: [Error: getaddrinfo ENOTFOUND cloudflare-ipfs.com] {
    errno: -3007,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'cloudflare-ipfs.com'
  }
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Updated ARREST: $178.68 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated metrics for 9hUjaf9R2kXaLN4Tjf8WR5CuaKXwNvcS8k7EzYrMpump in memory cache
Updated Pawlien: $1253971.46 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching bonding curve: Error: failed to get info about account 2y3mjybt5EhWbrfLZF8jtTFKGduHFqQvydnKnAKeCkfA: Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}
    at async getBondingCurveData (src/lib/market-data.ts:33:24)
    at async calculateTokenMetrics (src/lib/market-data.ts:80:20)
    at async eval (src/app/api/pump-monitor/route.ts:302:44)
  31 | ): Promise<BondingCurveData | null> {
  32 |   try {
> 33 |     const accountInfo = await connection.getAccountInfo(
     |                        ^
  34 |       new PublicKey(bondingCurveAddress)
  35 |     );
  36 |     
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Sano',
  symbol: 'SANO',
  mint: '2y3mjybt5EhWbrfLZF8jtTFKGduHFqQvydnKnAKeCkfA',
  marketCap: '$0.00',
  hasImage: false
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for FNKXfrTzGXq2uiM1Q7r8ftXqGGkcoQXeMKQ2nHeCpump in memory cache
Updated pooopwif: $167.29 MC
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:58:24)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
No tokens in cache to update
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'ZYNergy',
  symbol: '$ZYN',
  mint: 'B5nbJz76DT2fesfKbsMb4faWEy3qp36N1rtqYwU6pump',
  marketCap: '$689540.61',
  hasImage: true
}
Updated ЛУНА: $998215.48 MC
Finished updating token metrics
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'entry for everyone',
  symbol: 'hoe',
  mint: 'DURTuBFzfXSgxHEwbMTz9mVjrERPtDoE8BKwH8Ykpump',
  marketCap: '$694670.39',
  hasImage: true
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Error fetching metadata from https://ipfs.io/ipfs/bafkreidoqtiuyw24u3qcl6lcuir5zw2x6tzf45kybw55ytnbyk5rebrl5i : Error [AbortError]: This operation was aborted
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:118:23)
    at async eval (src/app/api/pump-monitor/route.ts:278:43)
  116 |     
  117 |     try {
> 118 |       const response = await fetch(httpUri, { 
      |                       ^
  119 |         signal: controller.signal,
  120 |         headers: {
  121 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  code: 20,
  INDEX_SIZE_ERR: 1,
  DOMSTRING_SIZE_ERR: 2,
  HIERARCHY_REQUEST_ERR: 3,
  WRONG_DOCUMENT_ERR: 4,
  INVALID_CHARACTER_ERR: 5,
  NO_DATA_ALLOWED_ERR: 6,
  NO_MODIFICATION_ALLOWED_ERR: 7,
  NOT_FOUND_ERR: 8,
  NOT_SUPPORTED_ERR: 9,
  INUSE_ATTRIBUTE_ERR: 10,
  INVALID_STATE_ERR: 11,
  SYNTAX_ERR: 12,
  INVALID_MODIFICATION_ERR: 13,
  NAMESPACE_ERR: 14,
  INVALID_ACCESS_ERR: 15,
  VALIDATION_ERR: 16,
  TYPE_MISMATCH_ERR: 17,
  SECURITY_ERR: 18,
  NETWORK_ERR: 19,
  ABORT_ERR: 20,
  URL_MISMATCH_ERR: 21,
  QUOTA_EXCEEDED_ERR: 22,
  TIMEOUT_ERR: 23,
  INVALID_NODE_TYPE_ERR: 24,
  DATA_CLONE_ERR: 25
}
Error fetching SOL price: TypeError: Cannot read properties of undefined (reading 'usd')
    at getSOLPrice (src/lib/market-data.ts:20:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:48)
  18 |     );
  19 |     const data = await response.json();
> 20 |     return data.solana.usd || 100; // Fallback to $100
     |                       ^
  21 |   } catch (error) {
  22 |     console.error('Error fetching SOL price:', error);
  23 |     return 100; // Fallback price
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'LOOKS LIKE ALIEN',
  symbol: 'LLL',
  mint: 'ECDry86tDaSXbPSYVY1YKCmjUUnHFPPTJRiDukrEpump',
  marketCap: '$670651.22',
  hasImage: false
}
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Redis Client Error [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
[?25h
