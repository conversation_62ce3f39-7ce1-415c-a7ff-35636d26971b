{"name": "pulse", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@redis/client": "^5.5.6", "@solana/spl-token": "^0.4.13", "@solana/web3.js": "^1.98.2", "@types/bn.js": "^5.2.0", "bn.js": "^5.2.2", "bs58": "^6.0.0", "eventsource": "^4.0.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "redis": "^5.5.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5", "undici-types": "^7.11.0"}}