# PumpFun Token Monitor

A real-time monitoring system for new token launches on the PumpFun platform using Solana blockchain.

## Features

- Real-time token detection via WebSocket logs subscription
- Server-Sent Events (SSE) for live frontend updates
- Automatic metadata and image resolution from IPFS
- Display of social links (Twitter, Telegram, Website)
- Clean, responsive UI with Tailwind CSS
- No third-party APIs - uses native Solana RPC

## Setup

1. Install dependencies:
```bash
npm install
```

2. Configure environment variables in `.env.local`:
```env
HELIUS_RPC_URL=https://mainnet.helius-rpc.com/?api-key=YOUR_API_KEY
PUMPFUN_PROGRAM_ID=6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P
```

3. Run the development server:
```bash
npm run dev
```

4. Open [http://localhost:3000](http://localhost:3000) to view the monitor.

## How It Works

1. **Backend**: Subscribes to Solana logs for PumpFun program events
2. **Detection**: Identifies "Instruction: Create" events in transaction logs
3. **Decoding**: Extracts token data from base64-encoded program data
4. **Metadata**: Fetches additional info from IPFS/HTTP URIs
5. **Streaming**: Sends real-time updates to frontend via SSE

## Token Data

Each token includes:
- Name, Symbol, and Image
- Mint address and transaction signature
- Social links (Twitter, Telegram, Website)
- Creator address and bonding curve
- Standard supply: 1B tokens with 6 decimals

See `PUMPFUN_DATA_GUIDE.md` for detailed field descriptions.

## Tech Stack

- Next.js 15 with TypeScript
- Solana Web3.js for blockchain interaction
- Server-Sent Events for real-time updates
- Tailwind CSS for styling
