
> pulse@0.1.0 dev
> next dev

   ▲ Next.js 15.3.5
   - Local:        http://localhost:3000
   - Network:      http://***************:3000
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 2.4s
 ○ Compiling /api/pump-monitor ...
 ✓ Compiled /api/pump-monitor in 2.6s (996 modules)
Starting background updater for token metrics
Redis Client Error ECONNREFUSED
Running without Redis cache - tokens will not persist
Redis Client Error ECONNREFUSED
Redis Client Error ECONNREFUSED
Failed to connect to Redis: [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
No tokens in cache to update
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Vamplon',
  symbol: 'VAMPLON',
  mint: 'UkKJAASGv52mmqborS2UHTnFLuaycYkNjHjoE9Vpump',
  marketCap: '$0.00',
  hasImage: true
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Fiat Is Hopeless America Party',
  symbol: 'FIATAP',
  mint: '6RsjrhSMayqnxsRBQBATY6sEB6eoHk1pErFyiKiLQ86B',
  marketCap: '$0.00',
  hasImage: true
}
[?25h
