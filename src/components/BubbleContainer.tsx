'use client';

import { useState, useEffect, useRef, useCallback } from 'react';
import TokenBubble from './TokenBubble';

interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  showName?: boolean;
  createdOn?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  marketCap?: number;
  totalSupply?: number;
  priceSOL?: number;
  priceUSD?: number;
  liquiditySOL?: number;
  volume24h?: number;
  volumeTotal?: number;
  transactionCount?: number;
  isGraduated?: boolean;
  lastUpdated?: number;
}

interface BubbleData {
  token: TokenInfo;
  id: string;
  spawnTime: number;
}

interface BubbleContainerProps {
  tokens: TokenInfo[];
  className?: string;
}

export default function BubbleContainer({ tokens, className = '' }: BubbleContainerProps) {
  const [bubbles, setBubbles] = useState<BubbleData[]>([]);
  const [screenDimensions, setScreenDimensions] = useState({ width: 0, height: 0 });
  const processedTokensRef = useRef<Set<string>>(new Set());
  const bubbleCountRef = useRef(0);
  const containerRef = useRef<HTMLDivElement>(null);

  // Maximum number of simultaneous bubbles for performance
  const MAX_BUBBLES = 25;

  // Update screen dimensions
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setScreenDimensions({
          width: rect.width,
          height: rect.height
        });
      }
    };

    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () => window.removeEventListener('resize', updateDimensions);
  }, []);

  // Process new tokens and create bubbles
  useEffect(() => {
    tokens.forEach((token) => {
      // Skip if token already processed
      if (processedTokensRef.current.has(token.mint)) {
        return;
      }

      // Mark token as processed
      processedTokensRef.current.add(token.mint);

      // Create new bubble with slight delay for staggered appearance
      const delay = bubbleCountRef.current * 200; // 200ms between bubbles
      bubbleCountRef.current++;

      setTimeout(() => {
        setBubbles(prev => {
          // Remove oldest bubbles if we're at the limit
          const currentBubbles = prev.length >= MAX_BUBBLES 
            ? prev.slice(-(MAX_BUBBLES - 1))
            : prev;

          const newBubble: BubbleData = {
            token,
            id: `${token.mint}-${Date.now()}`,
            spawnTime: Date.now()
          };

          return [...currentBubbles, newBubble];
        });
      }, delay);
    });
  }, [tokens]);

  // Handle bubble expiration
  const handleBubbleExpire = useCallback((mint: string) => {
    setBubbles(prev => prev.filter(bubble => bubble.token.mint !== mint));
  }, []);

  // Cleanup old bubbles periodically
  useEffect(() => {
    const cleanup = setInterval(() => {
      const now = Date.now();
      setBubbles(prev => 
        prev.filter(bubble => now - bubble.spawnTime < 20000) // Remove bubbles older than 20 seconds
      );
    }, 5000); // Check every 5 seconds

    return () => clearInterval(cleanup);
  }, []);

  return (
    <div 
      ref={containerRef}
      className={`bubble-container-wrapper relative overflow-hidden ${className}`}
      style={{ minHeight: '100vh' }}
    >
      {/* Background overlay for better bubble visibility */}
      <div className="absolute inset-0 bg-gradient-to-t from-transparent via-transparent to-transparent pointer-events-none" />
      
      {/* Render bubbles */}
      {screenDimensions.width > 0 && screenDimensions.height > 0 && (
        <>
          {bubbles.map((bubbleData, index) => (
            <TokenBubble
              key={bubbleData.id}
              token={bubbleData.token}
              onExpire={handleBubbleExpire}
              startDelay={0} // Delay is handled in bubble creation
              screenWidth={screenDimensions.width}
              screenHeight={screenDimensions.height}
            />
          ))}
        </>
      )}

      {/* Bubble stats overlay */}
      <div className="absolute top-4 right-4 bg-black/20 backdrop-blur-sm rounded-lg px-3 py-2 text-white text-sm pointer-events-none">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
          <span>{bubbles.length} active bubbles</span>
        </div>
      </div>

      {/* Instructions overlay when no bubbles */}
      {bubbles.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          <div className="text-center">
            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-full flex items-center justify-center animate-pulse-slow border-2 border-purple-500/30">
              <svg className="w-10 h-10 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-gray-600 dark:text-gray-300 mb-2">
              Waiting for Token Bubbles...
            </h3>
            <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
              New PumpFun tokens will appear as floating bubbles. Hover over them to see detailed information!
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
