'use client';

import { useState, useEffect, useRef } from 'react';

interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  showName?: boolean;
  createdOn?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  marketCap?: number;
  totalSupply?: number;
  priceSOL?: number;
  priceUSD?: number;
  liquiditySOL?: number;
  volume24h?: number;
  volumeTotal?: number;
  transactionCount?: number;
  isGraduated?: boolean;
  lastUpdated?: number;
}

interface TokenBubbleProps {
  token: TokenInfo;
  onExpire: (mint: string) => void;
  startDelay?: number;
  screenWidth: number;
  screenHeight: number;
}

export default function TokenBubble({ 
  token, 
  onExpire, 
  startDelay = 0, 
  screenWidth, 
  screenHeight 
}: TokenBubbleProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [isSpawning, setIsSpawning] = useState(true);
  const bubbleRef = useRef<HTMLDivElement>(null);
  const animationRef = useRef<number>();
  const startTimeRef = useRef<number>();
  const positionRef = useRef({ x: 0, y: 0 });
  const velocityRef = useRef({ x: 0, y: 0 });

  // Bubble configuration
  const BUBBLE_SIZE = Math.random() * 40 + 60; // 60-100px diameter
  const FLOAT_SPEED = Math.random() * 0.8 + 0.5; // 0.5-1.3 pixels per frame
  const HORIZONTAL_DRIFT = Math.random() * 0.2 - 0.1; // -0.1 to 0.1 pixels per frame
  const LIFESPAN = 12000 + Math.random() * 6000; // 12-18 seconds
  const FADE_DURATION = 2000; // 2 seconds fade out
  const SPAWN_DURATION = 600; // Spawn animation duration

  // Initialize bubble position and velocity
  useEffect(() => {
    const startX = Math.random() * (screenWidth - BUBBLE_SIZE);
    const startY = screenHeight + BUBBLE_SIZE;
    positionRef.current = { x: startX, y: startY };

    // Initialize velocity with slight randomization
    velocityRef.current = {
      x: HORIZONTAL_DRIFT + (Math.random() - 0.5) * 0.1,
      y: -FLOAT_SPEED
    };

    // Start animation after delay
    const timer = setTimeout(() => {
      setIsVisible(true);
      startTimeRef.current = Date.now();
      animate();

      // End spawn animation
      setTimeout(() => {
        setIsSpawning(false);
      }, SPAWN_DURATION);
    }, startDelay);

    return () => clearTimeout(timer);
  }, [startDelay, screenWidth, screenHeight, BUBBLE_SIZE, HORIZONTAL_DRIFT, FLOAT_SPEED, SPAWN_DURATION]);

  // Enhanced animation loop with physics
  const animate = () => {
    if (!bubbleRef.current || !startTimeRef.current) return;

    const elapsed = Date.now() - startTimeRef.current;
    const progress = elapsed / LIFESPAN;

    if (progress >= 1) {
      // Bubble expired
      setIsVisible(false);
      setTimeout(() => onExpire(token.mint), 300);
      return;
    }

    if (!isHovered) {
      // Apply velocity to position
      positionRef.current.x += velocityRef.current.x;
      positionRef.current.y += velocityRef.current.y;

      // Add subtle floating motion
      const floatOffset = Math.sin(elapsed * 0.002) * 0.5;
      positionRef.current.x += floatOffset;

      // Bounce off horizontal boundaries
      if (positionRef.current.x <= 0 || positionRef.current.x >= screenWidth - BUBBLE_SIZE) {
        velocityRef.current.x *= -0.8; // Dampen and reverse
        positionRef.current.x = Math.max(0, Math.min(screenWidth - BUBBLE_SIZE, positionRef.current.x));
      }

      // Slight acceleration upward over time
      velocityRef.current.y -= 0.001;
    }

    // Calculate opacity (fade out in last 2 seconds)
    const fadeStart = (LIFESPAN - FADE_DURATION) / LIFESPAN;
    let opacity = 1;
    if (progress > fadeStart) {
      opacity = 1 - ((progress - fadeStart) / (1 - fadeStart));
    }

    // Apply transform and opacity with hardware acceleration
    if (bubbleRef.current) {
      bubbleRef.current.style.transform = `translate3d(${positionRef.current.x}px, ${positionRef.current.y}px, 0)`;
      bubbleRef.current.style.opacity = opacity.toString();
    }

    // Continue animation
    animationRef.current = requestAnimationFrame(animate);
  };

  // Cleanup animation on unmount
  useEffect(() => {
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, []);

  const handleMouseEnter = () => {
    setIsHovered(true);
    setShowTooltip(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setShowTooltip(false);
  };

  const truncateAddress = (address: string) => {
    return `${address.slice(0, 4)}...${address.slice(-4)}`;
  };

  const formatTime = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const seconds = Math.floor((diff % 60000) / 1000);
    
    if (minutes > 0) {
      return `${minutes}m ago`;
    }
    return `${seconds}s ago`;
  };

  if (!isVisible) return null;

  return (
    <>
      {/* Bubble */}
      <div
        ref={bubbleRef}
        className={`bubble-container fixed pointer-events-auto cursor-pointer z-20 transition-all duration-300 ${
          isHovered ? 'scale-110' : 'scale-100'
        } ${isSpawning ? 'bubble-spawn' : ''}`}
        style={{
          width: `${BUBBLE_SIZE}px`,
          height: `${BUBBLE_SIZE}px`,
        }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <div className={`bubble-content w-full h-full rounded-full bg-gradient-to-br from-white/80 to-white/60 dark:from-gray-800/80 dark:to-gray-900/60 backdrop-blur-sm border border-white/30 dark:border-gray-700/30 shadow-lg flex flex-col items-center justify-center p-2 hover:shadow-xl transition-all duration-300 ${
          !isHovered && !isSpawning ? 'bubble-float' : ''
        }`}>
          {/* Token Image */}
          <div className="relative mb-1">
            {token.image && !imageError ? (
              <img
                src={token.image}
                alt={token.name}
                className={`w-8 h-8 rounded-full object-cover transition-opacity duration-300 ${
                  imageLoaded ? 'opacity-100' : 'opacity-0'
                }`}
                onLoad={() => setImageLoaded(true)}
                onError={() => setImageError(true)}
              />
            ) : (
              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                <span className="text-xs font-bold text-white">
                  {token.symbol.slice(0, 2).toUpperCase()}
                </span>
              </div>
            )}
          </div>

          {/* Token Symbol */}
          <div className="text-xs font-bold text-gray-800 dark:text-white text-center leading-tight">
            ${token.symbol}
          </div>

          {/* Price/Market Cap */}
          <div className="text-xs text-gray-600 dark:text-gray-300 text-center leading-tight">
            {token.marketCap ? `$${(token.marketCap / 1000).toFixed(0)}K` : 
             token.priceUSD ? `$${token.priceUSD.toFixed(6)}` : 'New'}
          </div>
        </div>
      </div>

      {/* Tooltip */}
      {showTooltip && (
        <div
          className="fixed z-30 pointer-events-none"
          style={{
            left: `${positionRef.current.x + BUBBLE_SIZE + 10}px`,
            top: `${positionRef.current.y}px`,
            transform: 'translateY(-50%)',
          }}
        >
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl border border-gray-200 dark:border-gray-700 p-4 max-w-sm tooltip-enter">
            {/* Header */}
            <div className="flex items-center gap-3 mb-3">
              <div className="relative">
                {token.image && !imageError ? (
                  <img
                    src={token.image}
                    alt={token.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                    <span className="text-sm font-bold text-white">
                      {token.symbol.slice(0, 2).toUpperCase()}
                    </span>
                  </div>
                )}
              </div>
              <div>
                <h3 className="font-bold text-gray-800 dark:text-white text-sm">
                  {token.name}
                </h3>
                <div className="flex items-center gap-2">
                  <span className="px-2 py-0.5 bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 rounded-full text-xs font-medium">
                    ${token.symbol}
                  </span>
                  {token.isGraduated && (
                    <span className="px-1.5 py-0.5 bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 rounded-full text-xs">
                      🎓
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Description */}
            {token.description && (
              <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 line-clamp-2">
                {token.description}
              </p>
            )}

            {/* Quick Stats */}
            <div className="grid grid-cols-2 gap-2 mb-3">
              <div className="bg-gray-50 dark:bg-gray-700/50 p-2 rounded-lg">
                <div className="text-xs text-gray-500 dark:text-gray-400">Market Cap</div>
                <div className="text-sm font-bold text-gray-800 dark:text-white">
                  ${token.marketCap?.toLocaleString() || '0'}
                </div>
              </div>
              <div className="bg-gray-50 dark:bg-gray-700/50 p-2 rounded-lg">
                <div className="text-xs text-gray-500 dark:text-gray-400">Price</div>
                <div className="text-sm font-bold text-gray-800 dark:text-white">
                  ${token.priceUSD?.toFixed(8) || '0'}
                </div>
              </div>
            </div>

            {/* Social Links */}
            {(token.twitter || token.telegram || token.website) && (
              <div className="flex gap-2 mb-3">
                {token.twitter && (
                  <a
                    href={token.twitter}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 px-2 py-1 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded text-xs hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-colors"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                    </svg>
                    Twitter
                  </a>
                )}
                {token.telegram && (
                  <a
                    href={token.telegram}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-1 px-2 py-1 bg-cyan-50 dark:bg-cyan-900/20 text-cyan-600 dark:text-cyan-400 rounded text-xs hover:bg-cyan-100 dark:hover:bg-cyan-900/40 transition-colors"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 6.56c-.21 2.27-1.13 7.75-1.6 10.28-.2 1.07-.59 1.43-.96 1.47-.82.08-1.44-.54-2.24-1.06-1.24-.82-1.95-1.33-3.15-2.13-1.39-.92-.49-1.43.3-2.26.21-.22 3.82-3.5 3.89-3.8.01-.04.01-.18-.07-.26s-.2-.05-.29-.03c-.12.03-2.09 1.32-5.9 3.88-.56.38-1.06.57-1.52.56-.5-.01-1.46-.28-2.17-.51-.88-.28-1.57-.43-1.51-.91.03-.25.38-.51 1.04-.78 4.08-1.78 6.8-2.95 8.17-3.52 3.89-1.62 4.7-1.9 5.23-1.91.12 0 .38.03.55.17.14.13.18.29.2.43-.01.06-.01.24-.02.38z"/>
                    </svg>
                    Telegram
                  </a>
                )}
              </div>
            )}

            {/* Footer */}
            <div className="flex justify-between items-center text-xs text-gray-500 dark:text-gray-400">
              <span>{formatTime(token.timestamp)}</span>
              <a
                href={`https://pump.fun/${token.mint}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-purple-600 dark:text-purple-400 hover:underline"
              >
                View on Pump.fun
              </a>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
