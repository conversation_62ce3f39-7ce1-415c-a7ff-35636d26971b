import { NextRequest } from 'next/server';
import { Connection, PublicKey, Logs } from '@solana/web3.js';
import bs58 from 'bs58';
import crypto from 'crypto';
import { 
  addTokenToCache, 
  getCachedTokens,
  CachedTokenInfo 
} from '@/lib/redis';
import { 
  getTokenActivity,
  getSOLPrice 
} from '@/lib/market-data';
import { calculatePumpFunMetrics } from '@/lib/pumpfun-curve';
import { startBackgroundUpdater } from '@/lib/background-updater';

const PUMPFUN_PROGRAM_ID = process.env.PUMPFUN_PROGRAM_ID!;
const RPC_URL = process.env.HELIUS_RPC_URL!;

// Fallback RPC endpoints
const FALLBACK_RPC_URLS = [
  'https://api.mainnet-beta.solana.com',
  'https://solana-api.projectserum.com',
  'https://rpc.ankr.com/solana'
];

interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  showName?: boolean;
  createdOn?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  marketCap?: number;
  totalSupply?: number;
}

const encoder = new TextEncoder();

// Function to create connection with fallback
async function createConnectionWithFallback(): Promise<Connection> {
  // Try primary RPC first
  try {
    const connection = new Connection(RPC_URL, {
      commitment: 'confirmed',
      httpHeaders: {
        'User-Agent': 'PumpFun-Monitor/1.0'
      }
    });

    // Test the connection
    await connection.getSlot();
    console.log('Connected to primary RPC:', RPC_URL);
    return connection;
  } catch (error) {
    console.warn('Primary RPC failed:', error);
  }

  // Try fallback RPCs
  for (const fallbackUrl of FALLBACK_RPC_URLS) {
    try {
      const connection = new Connection(fallbackUrl, {
        commitment: 'confirmed',
        httpHeaders: {
          'User-Agent': 'PumpFun-Monitor/1.0'
        }
      });

      // Test the connection
      await connection.getSlot();
      console.log('Connected to fallback RPC:', fallbackUrl);
      return connection;
    } catch (error) {
      console.warn('Fallback RPC failed:', fallbackUrl, error);
    }
  }

  throw new Error('All RPC endpoints failed');
}

// Calculate the discriminator for "create" instruction
// In Anchor, instruction discriminator = first 8 bytes of sha256("global:create")
const calculateDiscriminator = (instructionName: string): Buffer => {
  const hash = crypto.createHash('sha256');
  hash.update(`global:${instructionName}`);
  return hash.digest().slice(0, 8);
};

const CREATE_DISCRIMINATOR = calculateDiscriminator('create');

// Function to decode the Program data from logs
const decodeProgramData = (base64Data: string): TokenInfo | null => {
  try {
    const data = Buffer.from(base64Data, 'base64');
    
    // Skip discriminator (8 bytes)
    let offset = 8;
    
    // Check if we have enough data
    if (data.length < offset + 4) return null;
    
    // Read string length and content for name
    const nameLen = data.readUInt32LE(offset);
    offset += 4;
    
    // Validate length
    if (nameLen > 100 || offset + nameLen > data.length) return null;
    
    const name = data.slice(offset, offset + nameLen).toString('utf8');
    offset += nameLen;
    
    // Check if we have enough data for symbol
    if (offset + 4 > data.length) return null;
    
    // Read string length and content for symbol
    const symbolLen = data.readUInt32LE(offset);
    offset += 4;
    
    // Validate length
    if (symbolLen > 50 || offset + symbolLen > data.length) return null;
    
    const symbol = data.slice(offset, offset + symbolLen).toString('utf8');
    offset += symbolLen;
    
    // Check if we have enough data for uri
    if (offset + 4 > data.length) return null;
    
    // Read string length and content for uri
    const uriLen = data.readUInt32LE(offset);
    offset += 4;
    
    // Validate length
    if (uriLen > 500 || offset + uriLen > data.length) return null;
    
    const uri = data.slice(offset, offset + uriLen).toString('utf8');
    offset += uriLen;
    
    return {
      name,
      symbol,
      uri,
      mint: '', // Will be filled from transaction
      timestamp: Date.now(),
      signature: '' // Will be filled from transaction
    };
  } catch (error) {
    console.error('Error decoding program data:', error);
    return null;
  }
};

// Function to convert IPFS URI to HTTP URL
const convertToHttpUrl = (uri: string): string => {
  if (uri.startsWith('ipfs://')) {
    return `https://ipfs.io/ipfs/${uri.slice(7)}`;
  }
  return uri;
};

// Function to fetch metadata from URI
const fetchTokenMetadata = async (uri: string): Promise<Partial<TokenInfo> | null> => {
  try {
    if (!uri) return null;
    
    // Convert IPFS URI to HTTP
    const httpUri = convertToHttpUrl(uri);
    
    // If it's already an image URL, return it directly
    if (httpUri.match(/\.(jpeg|jpg|gif|png|webp|svg)$/i) || httpUri.includes('twimg.com')) {
      return { image: httpUri };
    }
    
    // Otherwise fetch metadata JSON
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout
    
    try {
      const response = await fetch(httpUri, { 
        signal: controller.signal,
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)'
        }
      });
      
      clearTimeout(timeoutId);
      
      if (!response.ok) {
        return null;
      }
      
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const metadata = await response.json();
        const imageUri = metadata.image || metadata.icon || metadata.logoURI || metadata.logo;
        
        return {
          image: imageUri ? convertToHttpUrl(imageUri) : undefined,
          description: metadata.description,
          twitter: metadata.twitter,
          telegram: metadata.telegram,
          website: metadata.website,
          showName: metadata.showName,
          createdOn: metadata.createdOn
        };
      }
      
      return null;
    } catch (fetchError) {
      clearTimeout(timeoutId);
      console.error('Error fetching metadata from', httpUri, ':', fetchError);
      return null;
    }
  } catch (error) {
    console.error('Error in fetchTokenMetadata:', error);
    return null;
  }
};

export async function GET(request: NextRequest) {
  const stream = new ReadableStream({
    async start(controller) {
      let connection: Connection;

      try {
        connection = await createConnectionWithFallback();
      } catch (error) {
        console.error('Failed to create connection:', error);
        const errorEvent = `data: ${JSON.stringify({
          type: 'error',
          message: 'Unable to connect to Solana network. All RPC endpoints are unavailable.'
        })}\n\n`;
        controller.enqueue(encoder.encode(errorEvent));
        controller.close();
        return;
      }

      const sendEvent = (data: any) => {
        const event = `data: ${JSON.stringify(data)}\n\n`;
        controller.enqueue(encoder.encode(event));
      };

      sendEvent({ type: 'connected', message: 'Connected to Solana RPC' });

      // Start background updater for metrics
      startBackgroundUpdater(connection);
      
      // Send cached tokens immediately
      try {
        const cachedTokens = await getCachedTokens();
        if (cachedTokens.length > 0) {
          sendEvent({
            type: 'cached_tokens',
            data: cachedTokens,
            count: cachedTokens.length
          });
        } else {
          // Send demo tokens if no cached tokens (for demonstration)
          const demoTokens = [
            {
              mint: "DemoToken1pump",
              name: "Demo Token",
              symbol: "DEMO",
              uri: "https://example.com/demo.json",
              image: "https://via.placeholder.com/64/8b5cf6/ffffff?text=DEMO",
              description: "This is a demo token to showcase the enhanced UI design",
              timestamp: Date.now() - 300000, // 5 minutes ago
              signature: "demo_signature_1",
              marketCap: 15000,
              priceSOL: 0.000001,
              priceUSD: 0.0001,
              liquiditySOL: 5.5,
              volume24h: 12.3,
              volumeTotal: 45.6,
              transactionCount: 156,
              isGraduated: false,
              lastUpdated: Date.now(),
              addedAt: Date.now() - 300000
            },
            {
              mint: "DemoToken2pump",
              name: "Another Demo",
              symbol: "DEMO2",
              uri: "https://example.com/demo2.json",
              image: "https://via.placeholder.com/64/ec4899/ffffff?text=D2",
              description: "Second demo token with different metrics",
              twitter: "https://twitter.com/demo",
              telegram: "https://t.me/demo",
              website: "https://demo.com",
              timestamp: Date.now() - 600000, // 10 minutes ago
              signature: "demo_signature_2",
              marketCap: 75000,
              priceSOL: 0.000005,
              priceUSD: 0.0005,
              liquiditySOL: 15.2,
              volume24h: 89.1,
              volumeTotal: 234.5,
              transactionCount: 892,
              isGraduated: true,
              lastUpdated: Date.now(),
              addedAt: Date.now() - 600000
            }
          ];

          sendEvent({
            type: 'cached_tokens',
            data: demoTokens,
            count: demoTokens.length
          });
        }
      } catch (error) {
        console.error('Error loading cached tokens:', error);
      }

      // Subscribe to logs for PumpFun program with error handling
      let subscriptionId: number | null = null;

      try {
        subscriptionId = connection.onLogs(
          new PublicKey(PUMPFUN_PROGRAM_ID),
          async (logs: Logs) => {
          try {
            // Look for "Instruction: Create" in logs
            const createIndex = logs.logs.findIndex(log => log.includes('Instruction: Create'));
            
            if (createIndex !== -1) {
              // Look for the Program data log that follows
              const dataLogIndex = logs.logs.findIndex((log, index) => 
                index > createIndex && log.includes('Program data:')
              );
              
              if (dataLogIndex !== -1) {
                // Extract base64 data
                const dataLog = logs.logs[dataLogIndex];
                const base64Data = dataLog.split('Program data: ')[1];
                
                if (base64Data) {
                  const tokenData = decodeProgramData(base64Data);
                  
                  if (tokenData) {
                    // Get transaction to find mint address
                    try {
                      const tx = await connection.getParsedTransaction(logs.signature, {
                        maxSupportedTransactionVersion: 0,
                        commitment: 'confirmed'
                      });
                      
                      if (tx && tx.meta && !tx.meta.err) {
                        // Find the mint address from the transaction
                        // PumpFun creates new token accounts, look for mint initialization
                        const postTokenBalances = tx.meta.postTokenBalances || [];
                        for (const balance of postTokenBalances) {
                          if (balance.mint && balance.uiTokenAmount.decimals === 6) {
                            tokenData.mint = balance.mint;
                            break;
                          }
                        }
                        
                        // If no mint found in balances, look in account keys
                        if (!tokenData.mint && tx.transaction.message.accountKeys) {
                          const accountKeys = tx.transaction.message.accountKeys;
                          // The mint is typically one of the first few accounts
                          for (let i = 0; i < Math.min(5, accountKeys.length); i++) {
                            const key = accountKeys[i];
                            if ('pubkey' in key) {
                              const pubkey = key.pubkey.toBase58();
                              // Check if it ends with "pump"
                              if (pubkey.endsWith('pump')) {
                                tokenData.mint = pubkey;
                                break;
                              }
                            }
                          }
                        }
                        
                        // Extract bonding curve and creator from transaction
                        if (tx.transaction.message.accountKeys) {
                          const accountKeys = tx.transaction.message.accountKeys;
                          // Usually the fee payer (first account) is the creator
                          if (accountKeys.length > 0 && 'pubkey' in accountKeys[0]) {
                            tokenData.creator = accountKeys[0].pubkey.toBase58();
                          }
                          
                          // Look for bonding curve account (usually contains specific pattern)
                          for (const key of accountKeys) {
                            if ('pubkey' in key) {
                              const pubkey = key.pubkey.toBase58();
                              // Bonding curves often have specific patterns
                              if (!pubkey.endsWith('pump') && 
                                  pubkey !== PUMPFUN_PROGRAM_ID && 
                                  pubkey !== tokenData.creator &&
                                  pubkey !== 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA' &&
                                  pubkey !== '11111111111111111111111111111111') {
                                // This could be the bonding curve
                                tokenData.bondingCurve = pubkey;
                                break;
                              }
                            }
                          }
                        }
                        
                        tokenData.signature = logs.signature;
                        
                        // Fetch metadata from URI
                        if (tokenData.uri) {
                          const metadata = await fetchTokenMetadata(tokenData.uri);
                          if (metadata) {
                            // Merge all metadata fields
                            Object.assign(tokenData, metadata);
                            
                            // If still no image, check if URI is an image
                            if (!tokenData.image) {
                              const httpUri = convertToHttpUrl(tokenData.uri);
                              if (httpUri.match(/\.(jpeg|jpg|gif|png|webp|svg)$/i) || httpUri.includes('twimg.com')) {
                                tokenData.image = httpUri;
                              }
                            }
                          }
                        }
                        
                        // Add standard PumpFun token data
                        tokenData.totalSupply = 1000000000; // 1 billion tokens standard for PumpFun
                        
                        // Fetch initial market metrics
                        try {
                          if (tokenData.bondingCurve) {
                            const solPriceUSD = await getSOLPrice();
                            
                            // Get market metrics using PumpFun-specific calculations
                            const metrics = await calculatePumpFunMetrics(
                              connection,
                              tokenData.bondingCurve,
                              solPriceUSD
                            );
                            
                            // Get initial activity (just count this creation)
                            const activity = {
                              volume24h: 0,
                              volumeTotal: 0,
                              transactionCount: 1 // Creation transaction
                            };
                            
                            // Create cached token object
                            const cachedToken: CachedTokenInfo = {
                              ...tokenData,
                              marketCap: metrics?.marketCap || 0,
                              priceSOL: metrics?.priceSOL || 0,
                              priceUSD: metrics?.priceUSD || 0,
                              liquiditySOL: metrics?.liquiditySOL || 0,
                              isGraduated: metrics?.isGraduated || false,
                              volume24h: activity.volume24h,
                              volumeTotal: activity.volumeTotal,
                              transactionCount: activity.transactionCount,
                              lastUpdated: Date.now(),
                              addedAt: Date.now()
                            };
                            
                            // Add to Redis cache (will auto-evict oldest if > 10)
                            await addTokenToCache(cachedToken);
                            
                            // Send the enhanced token data
                            sendEvent({ type: 'token', data: cachedToken });
                            
                            console.log('New PumpFun token detected and cached:', {
                              name: cachedToken.name,
                              symbol: cachedToken.symbol,
                              mint: cachedToken.mint,
                              marketCap: `$${cachedToken.marketCap.toFixed(2)}`,
                              hasImage: !!cachedToken.image
                            });
                          } else {
                            // No bonding curve, send basic data
                            sendEvent({ type: 'token', data: tokenData });
                          }
                        } catch (metricsError) {
                          console.error('Error fetching initial metrics:', metricsError);
                          // Still send token even if metrics fail
                          sendEvent({ type: 'token', data: tokenData });
                        }
                      }
                    } catch (txError) {
                      console.error('Error fetching transaction:', txError);
                    }
                  }
                }
              }
            }
            } catch (error) {
              console.error('Error processing logs:', error);
            }
          },
          'confirmed'
        );
      } catch (subscriptionError) {
        console.error('Failed to subscribe to logs:', subscriptionError);
        sendEvent({
          type: 'error',
          message: 'WebSocket subscription failed due to rate limiting. Using polling mode.'
        });

        // Fallback to polling mode
        const pollForNewTokens = async () => {
          try {
            // Get recent signatures for the PumpFun program
            const signatures = await connection.getSignaturesForAddress(
              new PublicKey(PUMPFUN_PROGRAM_ID),
              { limit: 5 }
            );

            // Process recent transactions (this is a simplified approach)
            // In a real implementation, you'd want to track processed signatures
            console.log(`Polling: Found ${signatures.length} recent transactions`);

          } catch (pollError) {
            console.error('Polling error:', pollError);
          }
        };

        // Poll every 30 seconds
        const pollInterval = setInterval(pollForNewTokens, 30000);

        // Clean up polling on disconnect
        request.signal.addEventListener('abort', () => {
          clearInterval(pollInterval);
        });

        // Send initial poll
        pollForNewTokens();
      }

      // Keep connection alive
      const keepAlive = setInterval(() => {
        sendEvent({ type: 'ping', timestamp: Date.now() });
      }, 30000);

      // Handle client disconnect
      request.signal.addEventListener('abort', () => {
        if (subscriptionId !== null) {
          try {
            connection.removeOnLogsListener(subscriptionId);
          } catch (error) {
            console.error('Error removing logs listener:', error);
          }
        }
        clearInterval(keepAlive);
        controller.close();
      });
    }
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}