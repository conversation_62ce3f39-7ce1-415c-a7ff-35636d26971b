'use client';

import { useEffect, useState, useRef } from 'react';
import Image from 'next/image';

interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  showName?: boolean;
  createdOn?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  marketCap?: number;
  totalSupply?: number;
  priceSOL?: number;
  priceUSD?: number;
  liquiditySOL?: number;
  volume24h?: number;
  volumeTotal?: number;
  transactionCount?: number;
  isGraduated?: boolean;
  lastUpdated?: number;
}

export default function Home() {
  const [tokens, setTokens] = useState<TokenInfo[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [newTokenIds, setNewTokenIds] = useState<Set<string>>(new Set());
  const [imageLoadingStates, setImageLoadingStates] = useState<Record<string, boolean>>({});
  const prevTokenCountRef = useRef(0);

  useEffect(() => {
    const eventSource = new EventSource('/api/pump-monitor');

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (data.type === 'connected') {
          setIsConnected(true);
          setError(null);
        } else if (data.type === 'cached_tokens') {
          // Load cached tokens (already sorted by newest first)
          setTokens(data.data);
          prevTokenCountRef.current = data.data.length;
          console.log(`Loaded ${data.count} cached tokens`);
        } else if (data.type === 'token') {
          // Add new token and maintain 10 token limit
          setTokens(prev => {
            const updated = [data.data, ...prev];
            const newTokens = updated.slice(0, 10); // Only keep 10 tokens

            // Mark new token for animation
            if (prev.length > 0) {
              setNewTokenIds(prevIds => new Set([...prevIds, data.data.mint]));
              // Remove the animation class after animation completes
              setTimeout(() => {
                setNewTokenIds(prevIds => {
                  const newIds = new Set(prevIds);
                  newIds.delete(data.data.mint);
                  return newIds;
                });
              }, 600);
            }

            return newTokens;
          });
        } else if (data.type === 'error') {
          setError(data.message);
        }
      } catch (err) {
        console.error('Error parsing event:', err);
      }
    };

    eventSource.onerror = () => {
      setIsConnected(false);
      setError('Connection lost. Attempting to reconnect...');
    };

    return () => {
      eventSource.close();
    };
  }, []);

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  };

  const truncateAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  const handleImageLoad = (mint: string) => {
    setImageLoadingStates(prev => ({ ...prev, [mint]: false }));
  };

  const handleImageLoadStart = (mint: string) => {
    setImageLoadingStates(prev => ({ ...prev, [mint]: true }));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900 transition-all duration-500">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25px 25px, rgba(156, 146, 172, 0.1) 2px, transparent 0)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="relative z-10 p-8 pb-20 font-[family-name:var(--font-geist-sans)]">
        <main className="max-w-7xl mx-auto">
          <div className="mb-12 text-center animate-fade-in">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 gradient-text">
              PumpFun Token Monitor
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Real-time monitoring of new token launches on the PumpFun platform
            </p>

            <div className="flex items-center justify-center gap-6 flex-wrap">
              <div className={`flex items-center gap-3 px-4 py-2 rounded-full backdrop-blur-sm border transition-all duration-300 ${
                isConnected
                  ? 'connection-indicator connected bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                  : 'connection-indicator disconnected bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
              }`}>
                <div className={`w-3 h-3 rounded-full ${
                  isConnected ? 'bg-green-500 animate-pulse-slow' : 'bg-red-500 animate-pulse'
                }`} />
                <span className="font-medium">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>

              {error && (
                <div className="px-4 py-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-full animate-fade-in">
                  <span className="text-red-600 dark:text-red-400 text-sm font-medium">{error}</span>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-2xl p-8 shadow-xl border border-white/20 dark:border-gray-700/20">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"></div>
              <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                New Tokens
              </h2>
              <div className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <span className="text-sm font-medium text-purple-700 dark:text-purple-300">Real-time</span>
              </div>
            </div>

            {tokens.length === 0 ? (
              <div className="text-center py-16">
                <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center animate-pulse-slow">
                  <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <p className="text-xl font-medium text-gray-600 dark:text-gray-300 mb-2">
                  Waiting for new tokens...
                </p>
                <p className="text-gray-500 dark:text-gray-400">
                  New PumpFun tokens will appear here automatically
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-3">
                {tokens.map((token, index) => (
                  <div
                    key={`${token.mint}-${index}`}
                    className={`token-card rounded-xl p-4 ${
                      newTokenIds.has(token.mint) ? 'animate-fade-in-up' : 'animate-fade-in'
                    }`}
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="flex items-start gap-4">
                      {/* Token Image */}
                      <div className="flex-shrink-0">
                        <div className="token-image-container w-14 h-14 relative">
                          {token.image ? (
                            <>
                              {imageLoadingStates[token.mint] && (
                                <div className="absolute inset-0 token-image-loading rounded-full"></div>
                              )}
                              <img
                                src={token.image}
                                alt={token.name}
                                className="token-image w-full h-full rounded-full object-cover ring-2 ring-purple-500/20 shadow-md"
                                onLoadStart={() => handleImageLoadStart(token.mint)}
                                onLoad={() => handleImageLoad(token.mint)}
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  const placeholder = target.nextElementSibling;
                                  if (placeholder) placeholder.classList.remove('hidden');
                                  handleImageLoad(token.mint);
                                }}
                              />
                            </>
                          ) : null}
                          <div className={`w-full h-full rounded-full bg-gradient-to-br from-purple-500 via-pink-500 to-purple-600 flex items-center justify-center ring-2 ring-purple-500/20 shadow-md ${token.image ? 'hidden' : ''}`}>
                            <span className="text-sm font-bold text-white drop-shadow-lg">
                              {token.symbol.slice(0, 2).toUpperCase()}
                            </span>
                          </div>
                        </div>
                      </div>

                      {/* Token Details */}
                      <div className="flex-1">
                        <div className="flex items-start justify-between mb-3">
                          <div>
                            <h3 className="font-bold text-lg text-gray-800 dark:text-white mb-1">
                              {token.name}
                            </h3>
                            <div className="flex items-center gap-2">
                              <span className="px-2 py-0.5 bg-gradient-to-r from-purple-100 to-pink-100 dark:from-purple-900/30 dark:to-pink-900/30 rounded-full text-xs font-semibold text-purple-700 dark:text-purple-300">
                                ${token.symbol}
                              </span>
                              {token.isGraduated && (
                                <span className="px-1.5 py-0.5 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 rounded-full text-xs font-medium text-green-700 dark:text-green-300">
                                  🎓 Graduated
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="text-right">
                            <span className="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-0.5 rounded-full">
                              {formatTime(token.timestamp)}
                            </span>
                          </div>
                        </div>

                        {/* Description */}
                        {token.description && (
                          <p className="text-xs text-gray-600 dark:text-gray-300 mb-3 leading-relaxed line-clamp-2 bg-gray-50 dark:bg-gray-700/30 p-2 rounded-lg">
                            {token.description}
                          </p>
                        )}

                        {/* Market Metrics */}
                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 mb-4">
                          <div className="bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-2 rounded-lg border border-green-200/50 dark:border-green-700/30">
                            <div className="text-xs font-medium text-green-600 dark:text-green-400 mb-0.5">Market Cap</div>
                            <div className="text-sm font-bold text-green-700 dark:text-green-300">
                              ${token.marketCap?.toLocaleString() || '0'}
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 p-2 rounded-lg border border-blue-200/50 dark:border-blue-700/30">
                            <div className="text-xs font-medium text-blue-600 dark:text-blue-400 mb-0.5">Price</div>
                            <div className="text-sm font-bold text-blue-700 dark:text-blue-300">
                              ${token.priceUSD?.toFixed(8) || '0'}
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-2 rounded-lg border border-purple-200/50 dark:border-purple-700/30">
                            <div className="text-xs font-medium text-purple-600 dark:text-purple-400 mb-0.5">Liquidity</div>
                            <div className="text-sm font-bold text-purple-700 dark:text-purple-300">
                              {token.liquiditySOL?.toFixed(4) || '0'} SOL
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-2 rounded-lg border border-orange-200/50 dark:border-orange-700/30">
                            <div className="text-xs font-medium text-orange-600 dark:text-orange-400 mb-0.5">24h Volume</div>
                            <div className="text-sm font-bold text-orange-700 dark:text-orange-300">
                              {token.volume24h?.toFixed(2) || '0'} SOL
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 p-2 rounded-lg border border-indigo-200/50 dark:border-indigo-700/30">
                            <div className="text-xs font-medium text-indigo-600 dark:text-indigo-400 mb-0.5">Total Volume</div>
                            <div className="text-sm font-bold text-indigo-700 dark:text-indigo-300">
                              {token.volumeTotal?.toFixed(2) || '0'} SOL
                            </div>
                          </div>
                          <div className="bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-800/50 dark:to-slate-800/50 p-2 rounded-lg border border-gray-200/50 dark:border-gray-600/30">
                            <div className="text-xs font-medium text-gray-600 dark:text-gray-400 mb-0.5">Transactions</div>
                            <div className="text-sm font-bold text-gray-700 dark:text-gray-300">
                              {token.transactionCount || '0'}
                            </div>
                          </div>
                        </div>

                        {/* Token Info Grid */}
                        <div className="grid grid-cols-2 gap-3 mb-4 p-3 bg-gray-50 dark:bg-gray-700/30 rounded-lg border-t border-gray-200 dark:border-gray-600">
                          <div className="space-y-0.5">
                            <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Mint Address</span>
                            <a
                              href={`https://solscan.io/token/${token.mint}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="social-link font-mono text-xs text-blue-600 dark:text-blue-400 hover:text-purple-600 dark:hover:text-purple-400 block"
                            >
                              {truncateAddress(token.mint)}
                            </a>
                          </div>
                          <div className="space-y-0.5">
                            <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Supply</span>
                            <span className="text-xs font-semibold text-gray-700 dark:text-gray-300 block">1B Tokens</span>
                          </div>
                          <div className="space-y-0.5">
                            <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Transaction</span>
                            <a
                              href={`https://solscan.io/tx/${token.signature}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="social-link text-xs text-blue-600 dark:text-blue-400 hover:text-purple-600 dark:hover:text-purple-400 block"
                            >
                              View on Solscan
                            </a>
                          </div>
                          <div className="space-y-0.5">
                            <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">Decimals</span>
                            <span className="text-xs font-semibold text-gray-700 dark:text-gray-300 block">6</span>
                          </div>
                        </div>
                      
                        {/* Social Links */}
                        {(token.twitter || token.telegram || token.website) && (
                          <div className="flex items-center gap-2 pt-3 border-t border-gray-200 dark:border-gray-600">
                            {token.twitter && (
                              <a
                                href={token.twitter}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="social-link flex items-center gap-1.5 px-2 py-1.5 bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400 rounded-md hover:bg-blue-100 dark:hover:bg-blue-900/40 transition-all"
                              >
                                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                                </svg>
                                <span className="text-xs font-medium">Twitter</span>
                              </a>
                            )}
                            {token.telegram && (
                              <a
                                href={token.telegram}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="social-link flex items-center gap-1.5 px-2 py-1.5 bg-cyan-50 dark:bg-cyan-900/20 text-cyan-600 dark:text-cyan-400 rounded-md hover:bg-cyan-100 dark:hover:bg-cyan-900/40 transition-all"
                              >
                                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 24 24">
                                  <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 6.56c-.21 2.27-1.13 7.75-1.6 10.28-.2 1.07-.59 1.43-.96 1.47-.82.08-1.44-.54-2.24-1.06-1.24-.82-1.95-1.33-3.15-2.13-1.39-.92-.49-1.43.3-2.26.21-.22 3.82-3.5 3.89-3.8.01-.04.01-.18-.07-.26s-.2-.05-.29-.03c-.12.03-2.09 1.32-5.9 3.88-.56.38-1.06.57-1.52.56-.5-.01-1.46-.28-2.17-.51-.88-.28-1.57-.43-1.51-.91.03-.25.38-.51 1.04-.78 4.08-1.78 6.8-2.95 8.17-3.52 3.89-1.62 4.7-1.9 5.23-1.91.12 0 .38.03.55.17.14.13.18.29.2.43-.01.06-.01.24-.02.38z"/>
                                </svg>
                                <span className="text-xs font-medium">Telegram</span>
                              </a>
                            )}
                          {token.website && (
                            <a
                              href={token.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="social-link flex items-center gap-1.5 px-2 py-1.5 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-md hover:bg-green-100 dark:hover:bg-green-900/40 transition-all"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 919-9" />
                              </svg>
                              <span className="text-xs font-medium">Website</span>
                            </a>
                          )}
                            <a
                              href={`https://pump.fun/${token.mint}`}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="social-link flex items-center gap-1.5 px-2 py-1.5 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 text-purple-600 dark:text-purple-400 rounded-md hover:from-purple-100 hover:to-pink-100 dark:hover:from-purple-900/40 dark:hover:to-pink-900/40 transition-all ml-auto"
                            >
                              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                              </svg>
                              <span className="text-xs font-medium">Pump.fun</span>
                            </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

          <div className="mt-12 text-center">
            <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/50">
              <div className="flex items-center justify-center gap-2 mb-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse-slow"></div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                  Monitoring PumpFun Program
                </span>
              </div>
              <p className="font-mono text-xs text-gray-500 dark:text-gray-400 mb-3">
                6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P
              </p>
              <div className="flex items-center justify-center gap-4 text-xs text-gray-400 dark:text-gray-500">
                <span>Powered by Helius RPC</span>
                <span>•</span>
                <span>Real-time Solana Monitoring</span>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}