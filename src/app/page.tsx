'use client';

import { useEffect, useState, useRef } from 'react';
import Image from 'next/image';
import B<PERSON>bleContainer from '../components/BubbleContainer';

interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  showName?: boolean;
  createdOn?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  marketCap?: number;
  totalSupply?: number;
  priceSOL?: number;
  priceUSD?: number;
  liquiditySOL?: number;
  volume24h?: number;
  volumeTotal?: number;
  transactionCount?: number;
  isGraduated?: boolean;
  lastUpdated?: number;
}

export default function Home() {
  const [tokens, setTokens] = useState<TokenInfo[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const eventSource = new EventSource('/api/pump-monitor');

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);

        if (data.type === 'connected') {
          setIsConnected(true);
          setError(null);
        } else if (data.type === 'cached_tokens') {
          // Load cached tokens (already sorted by newest first)
          setTokens(data.data);
          console.log(`Loaded ${data.count} cached tokens`);
        } else if (data.type === 'token') {
          // Add new token to the beginning of the array
          setTokens(prev => [data.data, ...prev]);
        } else if (data.type === 'error') {
          setError(data.message);
        }
      } catch (err) {
        console.error('Error parsing event:', err);
      }
    };

    eventSource.onerror = () => {
      setIsConnected(false);
      setError('Connection lost. Attempting to reconnect...');
    };

    return () => {
      eventSource.close();
    };
  }, []);



  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900 transition-all duration-500">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-30">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 25px 25px, rgba(156, 146, 172, 0.1) 2px, transparent 0)`,
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="relative z-10 p-8 pb-20 font-[family-name:var(--font-geist-sans)]">
        <main className="max-w-7xl mx-auto">
          <div className="mb-12 text-center animate-fade-in">
            <h1 className="text-5xl md:text-6xl font-bold mb-6 gradient-text">
              PumpFun Token Monitor
            </h1>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-8 max-w-2xl mx-auto">
              Real-time monitoring of new token launches on the PumpFun platform
            </p>

            <div className="flex items-center justify-center gap-6 flex-wrap">
              <div className={`flex items-center gap-3 px-4 py-2 rounded-full backdrop-blur-sm border transition-all duration-300 ${
                isConnected
                  ? 'connection-indicator connected bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                  : 'connection-indicator disconnected bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800'
              }`}>
                <div className={`w-3 h-3 rounded-full ${
                  isConnected ? 'bg-green-500 animate-pulse-slow' : 'bg-red-500 animate-pulse'
                }`} />
                <span className="font-medium">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>

              {error && (
                <div className="px-4 py-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-full animate-fade-in">
                  <span className="text-red-600 dark:text-red-400 text-sm font-medium">{error}</span>
                </div>
              )}
            </div>
          </div>

          {/* Header Section */}
          <div className="bg-white/70 dark:bg-gray-800/70 backdrop-blur-xl rounded-2xl p-6 shadow-xl border border-white/20 dark:border-gray-700/20 mb-6">
            <div className="flex items-center gap-3">
              <div className="w-2 h-8 bg-gradient-to-b from-purple-500 to-pink-500 rounded-full"></div>
              <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
                Token Bubble Stream
              </h2>
              <div className="px-3 py-1 bg-purple-100 dark:bg-purple-900/30 rounded-full">
                <span className="text-sm font-medium text-purple-700 dark:text-purple-300">Real-time</span>
              </div>
            </div>
            <p className="text-gray-600 dark:text-gray-300 mt-3 text-sm">
              Watch new PumpFun tokens float up as bubbles. Hover over them to see detailed information!
            </p>
          </div>

          {/* Bubble Animation Container */}
          <div className="relative">
            <BubbleContainer
              tokens={tokens}
              className="min-h-screen rounded-2xl bg-gradient-to-b from-purple-50/20 via-transparent to-pink-50/20 dark:from-purple-900/10 dark:via-transparent dark:to-pink-900/10 border border-gray-200/30 dark:border-gray-700/30 backdrop-blur-sm"
            />
          </div>

          <div className="mt-12 text-center">
            <div className="bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/50 dark:border-gray-700/50">
              <div className="flex items-center justify-center gap-2 mb-3">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse-slow"></div>
                <span className="text-sm font-medium text-gray-600 dark:text-gray-300">
                  Monitoring PumpFun Program
                </span>
              </div>
              <p className="font-mono text-xs text-gray-500 dark:text-gray-400 mb-3">
                6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P
              </p>
              <div className="flex items-center justify-center gap-4 text-xs text-gray-400 dark:text-gray-500">
                <span>Powered by Helius RPC</span>
                <span>•</span>
                <span>Real-time Solana Monitoring</span>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}