'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';

interface TokenInfo {
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  showName?: boolean;
  createdOn?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  marketCap?: number;
  totalSupply?: number;
  priceSOL?: number;
  priceUSD?: number;
  liquiditySOL?: number;
  volume24h?: number;
  volumeTotal?: number;
  transactionCount?: number;
  isGraduated?: boolean;
  lastUpdated?: number;
}

export default function Home() {
  const [tokens, setTokens] = useState<TokenInfo[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const eventSource = new EventSource('/api/pump-monitor');

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        
        if (data.type === 'connected') {
          setIsConnected(true);
          setError(null);
        } else if (data.type === 'cached_tokens') {
          // Load cached tokens (already sorted by newest first)
          setTokens(data.data);
          console.log(`Loaded ${data.count} cached tokens`);
        } else if (data.type === 'token') {
          // Add new token and maintain 10 token limit
          setTokens(prev => {
            const updated = [data.data, ...prev];
            return updated.slice(0, 10); // Only keep 10 tokens
          });
        } else if (data.type === 'error') {
          setError(data.message);
        }
      } catch (err) {
        console.error('Error parsing event:', err);
      }
    };

    eventSource.onerror = () => {
      setIsConnected(false);
      setError('Connection lost. Attempting to reconnect...');
    };

    return () => {
      eventSource.close();
    };
  }, []);

  const formatTime = (timestamp: number) => {
    const date = new Date(timestamp);
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  };

  const truncateAddress = (address: string) => {
    return `${address.slice(0, 6)}...${address.slice(-4)}`;
  };

  return (
    <div className="min-h-screen p-8 pb-20 font-[family-name:var(--font-geist-sans)]">
      <main className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold mb-4">PumpFun Token Monitor</h1>
          <div className="flex items-center gap-4">
            <div className={`flex items-center gap-2 ${isConnected ? 'text-green-600' : 'text-red-600'}`}>
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-600' : 'bg-red-600'} animate-pulse`} />
              <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
            </div>
            {error && <span className="text-red-500 text-sm">{error}</span>}
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">New Tokens (Real-time)</h2>
          
          {tokens.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <p>Waiting for new tokens...</p>
              <p className="text-sm mt-2">New PumpFun tokens will appear here automatically</p>
            </div>
          ) : (
            <div className="space-y-4">
              {tokens.map((token, index) => (
                <div 
                  key={`${token.mint}-${index}`}
                  className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start gap-4">
                    {/* Token Image */}
                    <div className="flex-shrink-0">
                      {token.image ? (
                        <img 
                          src={token.image} 
                          alt={token.name}
                          className="w-16 h-16 rounded-full object-cover ring-2 ring-purple-500/20"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            const placeholder = target.nextElementSibling;
                            if (placeholder) placeholder.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <div className={`w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center ring-2 ring-purple-500/20 ${token.image ? 'hidden' : ''}`}>
                        <span className="text-sm font-bold text-white">{token.symbol.slice(0, 3).toUpperCase()}</span>
                      </div>
                    </div>
                    
                    {/* Token Details */}
                    <div className="flex-1">
                      <div className="flex items-start justify-between mb-2">
                        <div>
                          <h3 className="font-semibold text-lg">{token.name}</h3>
                          <p className="text-sm text-gray-500">${token.symbol}</p>
                        </div>
                        <span className="text-xs text-gray-500">{formatTime(token.timestamp)}</span>
                      </div>
                      
                      {/* Description */}
                      {token.description && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                          {token.description}
                        </p>
                      )}
                      
                      {/* Market Metrics */}
                      <div className="grid grid-cols-2 gap-2 mb-3 text-sm">
                        <div>
                          <span className="text-gray-500">Market Cap: </span>
                          <span className="font-medium text-green-600">
                            ${token.marketCap?.toLocaleString() || '0'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Price: </span>
                          <span className="font-medium">
                            ${token.priceUSD?.toFixed(8) || '0'}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Liquidity: </span>
                          <span className="font-medium">
                            {token.liquiditySOL?.toFixed(4) || '0'} SOL
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">24h Volume: </span>
                          <span className="font-medium">
                            {token.volume24h?.toFixed(2) || '0'} SOL
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Total Volume: </span>
                          <span className="font-medium">
                            {token.volumeTotal?.toFixed(2) || '0'} SOL
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Transactions: </span>
                          <span className="font-medium">
                            {token.transactionCount || '0'}
                          </span>
                        </div>
                      </div>
                      
                      {/* Token Info Grid */}
                      <div className="grid grid-cols-2 gap-2 mb-3 text-sm border-t pt-3">
                        <div>
                          <span className="text-gray-500">Mint: </span>
                          <a 
                            href={`https://solscan.io/token/${token.mint}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="font-mono text-blue-600 hover:underline"
                          >
                            {truncateAddress(token.mint)}
                          </a>
                        </div>
                        <div>
                          <span className="text-gray-500">Supply: </span>
                          <span className="font-medium">1B</span>
                        </div>
                        <div>
                          <span className="text-gray-500">TX: </span>
                          <a 
                            href={`https://solscan.io/tx/${token.signature}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-blue-600 hover:underline"
                          >
                            View
                          </a>
                        </div>
                        <div>
                          <span className="text-gray-500">Decimals: </span>
                          <span className="font-medium">6</span>
                        </div>
                      </div>
                      
                      {/* Graduation Status */}
                      {token.isGraduated && (
                        <div className="mb-3">
                          <span className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-2 py-1 rounded text-xs font-medium">
                            🎓 Graduated to Raydium
                          </span>
                        </div>
                      )}
                      
                      {/* Social Links */}
                      {(token.twitter || token.telegram || token.website) && (
                        <div className="flex items-center gap-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                          {token.twitter && (
                            <a
                              href={token.twitter}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 text-sm text-gray-600 hover:text-blue-500 transition-colors"
                            >
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                              </svg>
                              Twitter
                            </a>
                          )}
                          {token.telegram && (
                            <a
                              href={token.telegram}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 text-sm text-gray-600 hover:text-blue-500 transition-colors"
                            >
                              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm4.64 6.56c-.21 2.27-1.13 7.75-1.6 10.28-.2 1.07-.59 1.43-.96 1.47-.82.08-1.44-.54-2.24-1.06-1.24-.82-1.95-1.33-3.15-2.13-1.39-.92-.49-1.43.3-2.26.21-.22 3.82-3.5 3.89-3.8.01-.04.01-.18-.07-.26s-.2-.05-.29-.03c-.12.03-2.09 1.32-5.9 3.88-.56.38-1.06.57-1.52.56-.5-.01-1.46-.28-2.17-.51-.88-.28-1.57-.43-1.51-.91.03-.25.38-.51 1.04-.78 4.08-1.78 6.8-2.95 8.17-3.52 3.89-1.62 4.7-1.9 5.23-1.91.12 0 .38.03.55.17.14.13.18.29.2.43-.01.06-.01.24-.02.38z"/>
                              </svg>
                              Telegram
                            </a>
                          )}
                          {token.website && (
                            <a
                              href={token.website}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="flex items-center gap-1 text-sm text-gray-600 hover:text-blue-500 transition-colors"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                              </svg>
                              Website
                            </a>
                          )}
                          <a
                            href={`https://pump.fun/${token.mint}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="flex items-center gap-1 text-sm text-gray-600 hover:text-purple-500 transition-colors ml-auto"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                            </svg>
                            Pump.fun
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="mt-8 text-center text-sm text-gray-500">
          <p>Monitoring PumpFun Program: 6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P</p>
          <p>Powered by Helius RPC</p>
        </div>
      </main>
    </div>
  );
}