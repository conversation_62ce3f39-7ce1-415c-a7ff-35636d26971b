import { Connection, PublicKey } from '@solana/web3.js';

// Bonding curve data structure
interface BondingCurveData {
  priceSOL: number;
  liquiditySOL: number;
  virtualSolReserves: number;
  virtualTokenReserves: number;
}

// Cache SOL price to avoid frequent API calls
let cachedSOLPrice = 100;
let lastPriceFetch = 0;
const PRICE_CACHE_DURATION = 300000; // 5 minutes

// Get current SOL price in USD
export async function getSOLPrice(): Promise<number> {
  const now = Date.now();

  // Return cached price if it's still fresh
  if (now - lastPriceFetch < PRICE_CACHE_DURATION) {
    return cachedSOLPrice;
  }

  try {
    // You can use any price API - this is just an example
    // In production, use CoinGecko, Binance, or Jupiter Price API
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    const response = await fetch(
      'https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd',
      {
        signal: controller.signal,
        headers: {
          'User-Agent': 'PumpFun-Monitor/1.0'
        }
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    const price = data?.solana?.usd || cachedSOLPrice;

    // Update cache
    cachedSOLPrice = price;
    lastPriceFetch = now;

    return price;
  } catch (error) {
    console.error('Error fetching SOL price:', error);
    return cachedSOLPrice; // Return cached price on error
  }
}

// Decode bonding curve account data
export async function getBondingCurveData(
  connection: Connection,
  bondingCurveAddress: string
): Promise<BondingCurveData | null> {
  try {
    const accountInfo = await connection.getAccountInfo(
      new PublicKey(bondingCurveAddress)
    );
    
    if (!accountInfo || accountInfo.data.length < 32) {
      return null;
    }
    
    // PumpFun bonding curve layout:
    // 0-8: virtualSolReserves
    // 8-16: virtualTokenReserves  
    // 16-24: realSolReserves
    // 24-32: realTokenReserves
    
    const data = accountInfo.data;
    const virtualSolReserves = Number(data.readBigUInt64LE(0));
    const virtualTokenReserves = Number(data.readBigUInt64LE(8));
    const realSolReserves = Number(data.readBigUInt64LE(16));
    
    // Calculate price using AMM formula
    const priceInLamports = virtualSolReserves / virtualTokenReserves;
    const priceInSOL = priceInLamports / 1e9;
    
    return {
      priceSOL: priceInSOL,
      liquiditySOL: realSolReserves / 1e9,
      virtualSolReserves,
      virtualTokenReserves
    };
  } catch (error) {
    console.error('Error fetching bonding curve:', error);
    return null;
  }
}

// Calculate market metrics
export async function calculateTokenMetrics(
  connection: Connection,
  bondingCurveAddress: string,
  solPriceUSD: number
): Promise<{
  marketCap: number;
  priceSOL: number;
  priceUSD: number;
  liquiditySOL: number;
  isGraduated: boolean;
} | null> {
  const curveData = await getBondingCurveData(connection, bondingCurveAddress);
  
  if (!curveData) return null;
  
  const totalSupply = 1_000_000_000; // 1B tokens standard
  const priceUSD = curveData.priceSOL * solPriceUSD;
  const marketCap = priceUSD * totalSupply;
  
  // Tokens graduate at $69,000 market cap
  const isGraduated = marketCap >= 69000;
  
  return {
    marketCap,
    priceSOL: curveData.priceSOL,
    priceUSD,
    liquiditySOL: curveData.liquiditySOL,
    isGraduated
  };
}

// Get volume and transaction data for PumpFun tokens
export async function getTokenActivity(
  connection: Connection,
  bondingCurve: string,
  limit: number = 50
): Promise<{
  volume24h: number;
  volumeTotal: number;
  transactionCount: number;
}> {
  let volume24h = 0;
  let volumeTotal = 0;
  let transactionCount = 0;
  
  try {
    const signatures = await connection.getSignaturesForAddress(
      new PublicKey(bondingCurve),
      { limit }
    );
    
    if (signatures.length === 0) {
      return { volume24h: 0, volumeTotal: 0, transactionCount: 0 };
    }
    
    const dayAgo = Date.now() - 24 * 60 * 60 * 1000;
    
    // Process transactions to find buys and sells
    for (const sig of signatures) {
      try {
        const tx = await connection.getTransaction(sig.signature, {
          maxSupportedTransactionVersion: 0,
          commitment: 'confirmed'
        });
        
        if (!tx || !tx.meta || tx.meta.err) continue;
        
        const logs = tx.meta.logMessages || [];
        
        // Check if this is a buy or sell transaction
        const isBuy = logs.some(log => log.includes('Instruction: Buy'));
        const isSell = logs.some(log => log.includes('Instruction: Sell'));
        
        if (isBuy || isSell) {
          transactionCount++;
          
          // For PumpFun, the SOL amount is the balance change of the bonding curve
          const accountKeys = tx.transaction.message.accountKeys;
          const bcIndex = accountKeys.findIndex(
            key => key.toBase58() === bondingCurve
          );
          
          if (bcIndex !== -1 && tx.meta.preBalances && tx.meta.postBalances) {
            // Calculate SOL volume
            const preBalance = tx.meta.preBalances[bcIndex];
            const postBalance = tx.meta.postBalances[bcIndex];
            
            // For buys: bonding curve receives SOL (balance increases)
            // For sells: bonding curve sends SOL (balance decreases)
            const solAmount = Math.abs(postBalance - preBalance) / 1e9;
            
            volumeTotal += solAmount;
            
            // Check if within 24 hours
            const txTime = sig.blockTime ? sig.blockTime * 1000 : 0;
            if (txTime > dayAgo) {
              volume24h += solAmount;
            }
          }
        }
      } catch (txError) {
        // Skip individual transaction errors
        continue;
      }
    }
  } catch (error) {
    console.error('Error getting token activity:', error);
  }
  
  return { volume24h, volumeTotal, transactionCount };
}