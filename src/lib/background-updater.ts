import { Connection } from '@solana/web3.js';
import { 
  getCachedTokens, 
  updateTokenMetrics as updateSingleTokenMetrics,
  CachedTokenInfo 
} from './redis';
import { 
  getTokenActivity,
  getSOLPrice 
} from './market-data';
import { calculatePumpFunMetrics } from './pumpfun-curve';

// Update interval in milliseconds
const UPDATE_INTERVAL = 90000; // 1.5 minutes (reduced frequency to avoid rate limits)
const BATCH_SIZE = 2; // Update 2 tokens at a time (reduced to avoid rate limits)
const RETRY_DELAY = 5000; // 5 seconds delay between retries

let updateTimer: NodeJS.Timeout | null = null;
let isUpdating = false;

// Start background updater
export function startBackgroundUpdater(connection: Connection) {
  if (updateTimer) {
    console.log('Background updater already running');
    return;
  }
  
  console.log('Starting background updater for token metrics');
  
  // Run immediately
  updateAllTokenMetrics(connection);
  
  // Then run periodically
  updateTimer = setInterval(() => {
    updateAllTokenMetrics(connection);
  }, UPDATE_INTERVAL);
}

// Stop background updater
export function stopBackgroundUpdater() {
  if (updateTimer) {
    clearInterval(updateTimer);
    updateTimer = null;
    console.log('Background updater stopped');
  }
}

// Update all cached token metrics
async function updateAllTokenMetrics(connection: Connection) {
  if (isUpdating) {
    console.log('Update already in progress, skipping...');
    return;
  }
  
  isUpdating = true;
  
  try {
    // Get current SOL price
    const solPriceUSD = await getSOLPrice();
    
    // Get all cached tokens
    const tokens = await getCachedTokens();
    
    if (tokens.length === 0) {
      console.log('No tokens in cache to update');
      return;
    }
    
    console.log(`Updating metrics for ${tokens.length} tokens`);
    
    // Update tokens in batches
    for (let i = 0; i < tokens.length; i += BATCH_SIZE) {
      const batch = tokens.slice(i, i + BATCH_SIZE);
      
      await Promise.all(
        batch.map(async (token) => {
          try {
            // Skip if no bonding curve
            if (!token.bondingCurve) {
              console.log(`Skipping ${token.symbol} - no bonding curve`);
              return;
            }
            
            // Get market metrics using PumpFun-specific calculations
            const metricsPromise = calculatePumpFunMetrics(
              connection,
              token.bondingCurve,
              solPriceUSD
            );
            
            // Get activity data
            const activityPromise = getTokenActivity(
              connection,
              token.bondingCurve,
              50 // Only check last 50 transactions for performance
            );
            
            const [metrics, activity] = await Promise.all([
              metricsPromise.catch(error => {
                console.error(`Error fetching metrics for ${token.symbol}:`, error);
                return null;
              }),
              activityPromise.catch(error => {
                console.error(`Error fetching activity for ${token.symbol}:`, error);
                return { volume24h: 0, volumeTotal: 0, transactionCount: 0 };
              })
            ]);
            
            if (metrics) {
              // Update token in cache
              await updateSingleTokenMetrics(token.mint, {
                marketCap: metrics.marketCap,
                priceSOL: metrics.priceSOL,
                priceUSD: metrics.priceUSD,
                liquiditySOL: metrics.liquiditySOL,
                isGraduated: metrics.isGraduated,
                volume24h: activity.volume24h,
                volumeTotal: activity.volumeTotal,
                transactionCount: activity.transactionCount,
                lastUpdated: Date.now()
              });
              
              console.log(`Updated ${token.symbol}: $${metrics.marketCap.toFixed(2)} MC`);
            }
          } catch (error) {
            console.error(`Error updating ${token.symbol}:`, error);
          }
        })
      );
      
      // Longer delay between batches to avoid rate limits
      if (i + BATCH_SIZE < tokens.length) {
        await new Promise(resolve => setTimeout(resolve, 3000)); // 3 second delay
      }
    }
    
    console.log('Finished updating token metrics');
  } catch (error) {
    console.error('Error in background updater:', error);
  } finally {
    isUpdating = false;
  }
}

// Get updater status
export function getUpdaterStatus() {
  return {
    isRunning: updateTimer !== null,
    isUpdating,
    updateInterval: UPDATE_INTERVAL,
    batchSize: BATCH_SIZE
  };
}