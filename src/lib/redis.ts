import { createClient } from 'redis';

// Redis client singleton
let redisClient: ReturnType<typeof createClient> | null = null;
let redisConnectionFailed = false;

// Initialize Redis connection
export async function getRedisClient() {
  if (!redisClient && !redisConnectionFailed) {
    try {
      redisClient = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379',
        socket: {
          reconnectStrategy: (retries) => {
            if (retries > 3) {
              redisConnectionFailed = true;
              return false; // Stop retrying
            }
            return Math.min(retries * 50, 1000);
          }
        }
      });

      let errorCount = 0;
      redisClient.on('error', (err) => {
        errorCount++;
        if (errorCount <= 3) {
          console.error('Redis Client Error', err.code || err.message);
          if (errorCount === 1) {
            console.log('Running without Redis cache - tokens will not persist');
          }
        }
      });
      
      redisClient.on('connect', () => console.log('Redis Client Connected'));

      await redisClient.connect();
    } catch (error) {
      console.error('Failed to connect to Redis:', error);
      console.log('Running without Redis cache - tokens will not persist');
      // Continue without Redis - app will work but without caching
    }
  }

  return redisClient;
}

// Token cache key prefix
const TOKEN_CACHE_KEY = 'pumpfun:tokens';
const TOKEN_LIST_KEY = 'pumpfun:token_list';
const CACHE_SIZE = 10; // Only keep 10 tokens

// Enhanced TokenInfo with metrics
export interface CachedTokenInfo {
  // Basic info
  mint: string;
  name: string;
  symbol: string;
  uri: string;
  image?: string;
  description?: string;
  twitter?: string;
  telegram?: string;
  website?: string;
  timestamp: number;
  signature: string;
  bondingCurve?: string;
  creator?: string;
  
  // Market metrics
  marketCap: number;
  priceSOL: number;
  priceUSD: number;
  liquiditySOL: number;
  volume24h: number;
  volumeTotal: number;
  transactionCount: number;
  isGraduated: boolean;
  
  // Cache metadata
  lastUpdated: number;
  addedAt: number;
}

// In-memory fallback cache
const memoryCache: Map<string, CachedTokenInfo> = new Map();
const memoryCacheOrder: string[] = [];

// Add new token to cache (maintains 10 token limit)
export async function addTokenToCache(token: CachedTokenInfo): Promise<void> {
  const client = await getRedisClient();
  
  if (!client || !client.isReady) {
    // Fallback to memory cache
    memoryCache.set(token.mint, token);
    memoryCacheOrder.unshift(token.mint);
    
    // Maintain 10 token limit in memory
    if (memoryCacheOrder.length > CACHE_SIZE) {
      const removed = memoryCacheOrder.splice(CACHE_SIZE);
      removed.forEach(mint => memoryCache.delete(mint));
    }
    
    console.log('Token added to memory cache (Redis unavailable)');
    return;
  }
  
  try {
    // Add timestamp for cache management
    token.addedAt = Date.now();
    token.lastUpdated = Date.now();
    
    // Store token data
    await client.hSet(
      TOKEN_CACHE_KEY,
      token.mint,
      JSON.stringify(token)
    );
    
    // Add to sorted list (score is timestamp for ordering)
    await client.zAdd(TOKEN_LIST_KEY, {
      score: token.timestamp,
      value: token.mint
    });
    
    // Maintain only 10 tokens - remove oldest ones
    const totalTokens = await client.zCard(TOKEN_LIST_KEY);
    if (totalTokens > CACHE_SIZE) {
      // Get tokens to remove (oldest ones)
      const tokensToRemove = await client.zRange(
        TOKEN_LIST_KEY,
        0,
        totalTokens - CACHE_SIZE - 1
      );
      
      if (tokensToRemove.length > 0) {
        // Remove from hash
        await client.hDel(TOKEN_CACHE_KEY, tokensToRemove);
        
        // Remove from sorted set
        await client.zRem(TOKEN_LIST_KEY, tokensToRemove);
        
        console.log(`Evicted ${tokensToRemove.length} old tokens from cache`);
      }
    }
  } catch (error) {
    console.error('Error adding token to cache:', error);
  }
}

// Get all cached tokens (newest first)
export async function getCachedTokens(): Promise<CachedTokenInfo[]> {
  const client = await getRedisClient();
  
  if (!client || !client.isReady) {
    // Return from memory cache
    const tokens: CachedTokenInfo[] = [];
    for (const mint of memoryCacheOrder) {
      const token = memoryCache.get(mint);
      if (token) tokens.push(token);
    }
    return tokens;
  }
  
  try {
    // Get token mints in reverse order (newest first)
    const tokenMints = await client.zRange(TOKEN_LIST_KEY, 0, -1, { REV: true });
    
    if (tokenMints.length === 0) {
      return [];
    }
    
    // Get all token data
    const tokenData = await client.hmGet(TOKEN_CACHE_KEY, tokenMints);
    
    // Parse and return tokens
    const tokens: CachedTokenInfo[] = [];
    for (const data of tokenData) {
      if (data) {
        tokens.push(JSON.parse(data));
      }
    }
    
    return tokens;
  } catch (error) {
    console.error('Error getting cached tokens:', error);
    return [];
  }
}

// Update token metrics
export async function updateTokenMetrics(
  mint: string,
  metrics: Partial<CachedTokenInfo>
): Promise<void> {
  const client = await getRedisClient();
  
  if (!client || !client.isReady) {
    // Update in memory cache
    const token = memoryCache.get(mint);
    if (token) {
      Object.assign(token, metrics, { lastUpdated: Date.now() });
      console.log(`Updated metrics for ${mint} in memory cache`);
    }
    return;
  }
  
  try {
    // Get existing token data
    const existingData = await client.hGet(TOKEN_CACHE_KEY, mint);
    if (!existingData) {
      console.log(`Token ${mint} not found in cache`);
      return;
    }
    
    // Merge with new metrics
    const token: CachedTokenInfo = JSON.parse(existingData);
    const updatedToken: CachedTokenInfo = {
      ...token,
      ...metrics,
      lastUpdated: Date.now()
    };
    
    // Save updated token
    await client.hSet(
      TOKEN_CACHE_KEY,
      mint,
      JSON.stringify(updatedToken)
    );
    
    console.log(`Updated metrics for token ${mint}`);
  } catch (error) {
    console.error('Error updating token metrics:', error);
  }
}

// Get single token from cache
export async function getCachedToken(mint: string): Promise<CachedTokenInfo | null> {
  const client = await getRedisClient();
  
  try {
    const data = await client.hGet(TOKEN_CACHE_KEY, mint);
    if (!data) return null;
    
    return JSON.parse(data);
  } catch (error) {
    console.error('Error getting cached token:', error);
    return null;
  }
}

// Clear all cached tokens (useful for maintenance)
export async function clearTokenCache(): Promise<void> {
  const client = await getRedisClient();
  
  try {
    await client.del(TOKEN_CACHE_KEY);
    await client.del(TOKEN_LIST_KEY);
    console.log('Token cache cleared');
  } catch (error) {
    console.error('Error clearing cache:', error);
  }
}

// Get cache statistics
export async function getCacheStats(): Promise<{
  tokenCount: number;
  oldestToken: string | null;
  newestToken: string | null;
  cacheSize: number;
}> {
  const client = await getRedisClient();
  
  try {
    const tokenCount = await client.zCard(TOKEN_LIST_KEY);
    const oldest = await client.zRange(TOKEN_LIST_KEY, 0, 0);
    const newest = await client.zRange(TOKEN_LIST_KEY, -1, -1);
    
    return {
      tokenCount,
      oldestToken: oldest[0] || null,
      newestToken: newest[0] || null,
      cacheSize: CACHE_SIZE
    };
  } catch (error) {
    console.error('Error getting cache stats:', error);
    return {
      tokenCount: 0,
      oldestToken: null,
      newestToken: null,
      cacheSize: CACHE_SIZE
    };
  }
}

// Disconnect Redis (for cleanup)
export async function disconnectRedis(): Promise<void> {
  if (redisClient) {
    await redisClient.disconnect();
    redisClient = null;
  }
}