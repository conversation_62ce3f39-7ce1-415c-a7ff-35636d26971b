import { Connection, PublicKey } from '@solana/web3.js';
import BN from 'bn.js';

// PumpFun uses a virtual AMM bonding curve
// Initial virtual reserves when a token launches:
const INITIAL_VIRTUAL_TOKEN_RESERVES = 1_073_000_000_000_000; // 1.073B tokens (with 6 decimals)
const INITIAL_VIRTUAL_SOL_RESERVES = 30_000_000_000; // 30 SOL in lamports
const INITIAL_REAL_TOKEN_RESERVES = 793_100_000_000_000; // 793.1M tokens
const TOKEN_DECIMALS = 6;
const LAMPORTS_PER_SOL = 1_000_000_000;
const TOTAL_SUPPLY = 1_000_000_000; // 1 billion tokens

// PumpFun Bonding Curve Account Layout (total 176 bytes)
interface BondingCurveAccount {
  discriminator: Buffer;      // 8 bytes
  virtualTokenReserves: BN;   // 8 bytes - u64
  virtualSolReserves: BN;     // 8 bytes - u64
  realTokenReserves: BN;      // 8 bytes - u64
  realSolReserves: BN;        // 8 bytes - u64
  tokenTotalSupply: BN;       // 8 bytes - u64
  complete: boolean;          // 1 byte
  // ... other fields we don't need for price calculation
}

export async function getPumpFunCurveData(
  connection: Connection,
  bondingCurveAddress: string
): Promise<{
  priceSOL: number;
  marketCapSOL: number;
  liquiditySOL: number;
  virtualTokenReserves: number;
  virtualSolReserves: number;
  complete: boolean;
} | null> {
  try {
    const accountInfo = await connection.getAccountInfo(
      new PublicKey(bondingCurveAddress)
    );
    
    if (!accountInfo || accountInfo.data.length < 48) {
      console.log('Invalid bonding curve account');
      return null;
    }
    
    const data = accountInfo.data;
    let offset = 8; // Skip discriminator
    
    // Read the reserves safely
    try {
      const virtualTokenReserves = new BN(data.slice(offset, offset + 8), 'le');
      offset += 8;
      
      const virtualSolReserves = new BN(data.slice(offset, offset + 8), 'le');
      offset += 8;
      
      const realTokenReserves = new BN(data.slice(offset, offset + 8), 'le');
      offset += 8;
      
      const realSolReserves = new BN(data.slice(offset, offset + 8), 'le');
      offset += 8;
      
      const tokenTotalSupply = new BN(data.slice(offset, offset + 8), 'le');
      offset += 8;
      
      const complete = offset < data.length ? data[offset] === 1 : false;
    
    // PumpFun's market cap calculation is simply: virtualSolReserves / 1e9
    // This gives market cap in SOL
    const marketCapSOL = parseFloat(virtualSolReserves.toString()) / LAMPORTS_PER_SOL;
    
    // Calculate price per token
    // Price = virtualSolReserves / virtualTokenReserves (both in smallest units)
    // Then adjust for decimals
    const virtualSolFloat = parseFloat(virtualSolReserves.toString());
    const virtualTokenFloat = parseFloat(virtualTokenReserves.toString());
    
    // Price in SOL per whole token (accounting for 6 decimals)
    const pricePerToken = (virtualSolFloat / virtualTokenFloat) * Math.pow(10, TOKEN_DECIMALS);
    
      // Real SOL reserves represent the liquidity
      const liquiditySOL = parseFloat(realSolReserves.toString()) / LAMPORTS_PER_SOL;
      
      return {
        priceSOL: pricePerToken,
        marketCapSOL,
        liquiditySOL,
        virtualTokenReserves: parseFloat(virtualTokenReserves.toString()),
        virtualSolReserves: parseFloat(virtualSolReserves.toString()),
        complete
      };
    } catch (innerError) {
      console.error('Error parsing bonding curve data:', innerError);
      return null;
    }
  } catch (error) {
    console.error('Error reading bonding curve:', error);
    return null;
  }
}

// Calculate token metrics with proper PumpFun logic
export async function calculatePumpFunMetrics(
  connection: Connection,
  bondingCurveAddress: string,
  solPriceUSD: number
): Promise<{
  marketCap: number;
  priceSOL: number;
  priceUSD: number;
  liquiditySOL: number;
  liquidityUSD: number;
  isGraduated: boolean;
} | null> {
  const curveData = await getPumpFunCurveData(connection, bondingCurveAddress);
  
  if (!curveData) return null;
  
  const priceUSD = curveData.priceSOL * solPriceUSD;
  const marketCapUSD = curveData.marketCapSOL * solPriceUSD;
  const liquidityUSD = curveData.liquiditySOL * solPriceUSD;
  
  // PumpFun tokens graduate when they reach ~$69,000 market cap
  // or when the bonding curve is marked as complete
  const isGraduated = curveData.complete || marketCapUSD >= 69000;
  
  // Validate values to prevent negative or unrealistic market caps
  const validatedMarketCap = marketCapUSD > 0 && marketCapUSD < 1e12 ? marketCapUSD : 0;
  const validatedPrice = priceUSD > 0 && priceUSD < 1000 ? priceUSD : 0;
  
  return {
    marketCap: validatedMarketCap,
    priceSOL: curveData.priceSOL > 0 ? curveData.priceSOL : 0,
    priceUSD: validatedPrice,
    liquiditySOL: curveData.liquiditySOL,
    liquidityUSD: liquidityUSD > 0 ? liquidityUSD : 0,
    isGraduated
  };
}