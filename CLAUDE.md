# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

**Development Server**
```bash
NODE_OPTIONS='--no-deprecation' npm run dev
```
Note: Always use NODE_OPTIONS to suppress deprecation warnings. Do not use --turbopack flag.

**Build & Production**
```bash
npm run build
npm start
```

**Linting**
```bash
npm run lint
```

## Architecture Overview

This is a Next.js 15 application that monitors new token launches on the PumpFun platform (Solana blockchain) in real-time. It displays the 10 most recent tokens with market metrics that update every 60 seconds.

### Core Data Flow

1. **Token Detection**: WebSocket subscription to Solana logs → Parse "Create" instructions → Extract token metadata
2. **Market Data**: Read bonding curve accounts → Calculate price/market cap from virtual reserves
3. **Caching**: Store last 10 tokens in Redis (with in-memory fallback)
4. **Frontend Updates**: Server-Sent Events (SSE) stream real-time data to browser

### Key Technical Details

**PumpFun Program ID**: `6EF8rrecthR5Dkzon8Nwu78hRvfCKubJ14M5uBEwF6P`

**Market Cap Calculation**: PumpFun uses a unique formula where market cap in SOL equals virtualSolReserves / 1e9. This is different from traditional market cap calculations.

**Bonding Curve Constants**:
- Initial virtual token reserves: 1.073B tokens
- Initial virtual SOL reserves: 30 SOL
- Token decimals: 6
- Graduation threshold: ~$69,000 market cap

**Rate Limiting**: The Helius RPC endpoint has aggressive rate limits. The code includes exponential backoff retry logic.

### Critical Files

**`/src/app/api/pump-monitor/route.ts`** - Main API endpoint
- Subscribes to Solana logs for new tokens
- Decodes binary program data with bounds checking
- Streams updates via Server-Sent Events
- Handles IPFS metadata resolution

**`/src/lib/pumpfun-curve.ts`** - Market calculations
- Decodes bonding curve account data
- Calculates price using virtual AMM formula
- Validates market cap to prevent overflow errors

**`/src/lib/redis.ts`** - Caching layer
- Maintains exactly 10 tokens (FIFO eviction)
- Falls back to in-memory cache if Redis unavailable
- Provides atomic update operations

**`/src/lib/background-updater.ts`** - Metrics updater
- Runs every 60 seconds
- Updates in batches of 3 tokens
- Prevents concurrent executions

### Common Issues & Solutions

**BN.js Overflow**: Use `parseFloat(bn.toString())` instead of `bn.toNumber()`

**Hydration Errors**: Add `suppressHydrationWarning={true}` to body tag

**Invalid Bonding Curves**: Some tokens have malformed bonding curve accounts - always validate data length

**Rate Limiting**: Implement exponential backoff and batch requests

### Environment Variables

Required in `.env.local`:
- `HELIUS_RPC_URL` - Solana RPC endpoint
- `PUMPFUN_PROGRAM_ID` - PumpFun program address
- `REDIS_URL` - Optional Redis connection string