
> pulse@0.1.0 dev
> next dev

   ▲ Next.js 15.3.5
   - Local:        http://localhost:3000
   - Network:      http://***************:3000
   - Environments: .env.local

 ✓ Starting...
 ✓ Ready in 2.2s
 ○ Compiling /api/pump-monitor ...
 ✓ Compiled /api/pump-monitor in 4s (996 modules)
Starting background updater for token metrics
Redis Client Error ECONNREFUSED
Running without Redis cache - tokens will not persist
Redis Client Error ECONNREFUSED
Redis Client Error ECONNREFUSED
No tokens in cache to update
Failed to connect to Redis: [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Background updater already running
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:47)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:108:20)
    at async eval (src/app/api/pump-monitor/route.ts:323:44)
  66 |     // Calculate price: price = virtualSolReserves / virtualTokenReserves
  67 |     // This gives us the price of 1 token (with decimals) in lamports
> 68 |     const priceInLamports = virtualSolReserves.toNumber() / virtualTokenReserves.toNumber();
     |                                               ^
  69 |     
  70 |     // Convert to price per whole token in SOL
  71 |     const pricePerToken = (priceInLamports * Math.pow(10, TOKEN_DECIMALS)) / LAMPORTS_PER_SOL;
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Sheldon ',
  symbol: 'Sheldon ',
  mint: 'J3Fh2ruKQcxWWxqQRAnG7EXSHNjeUoRRQtCUE4txpump',
  marketCap: '$0.00',
  hasImage: true
}
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'عملة ميمز عربي',
  symbol: 'ميم',
  mint: 'B5Vkz1tZA2YXCraDA87jF2rWYHgmvzXHBqCao9thpump',
  marketCap: '$0.00',
  hasImage: true
}
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:47)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:108:20)
    at async eval (src/app/api/pump-monitor/route.ts:323:44)
  66 |     // Calculate price: price = virtualSolReserves / virtualTokenReserves
  67 |     // This gives us the price of 1 token (with decimals) in lamports
> 68 |     const priceInLamports = virtualSolReserves.toNumber() / virtualTokenReserves.toNumber();
     |                                               ^
  69 |     
  70 |     // Convert to price per whole token in SOL
  71 |     const pricePerToken = (priceInLamports * Math.pow(10, TOKEN_DECIMALS)) / LAMPORTS_PER_SOL;
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Sheldon ',
  symbol: 'Sheldon ',
  mint: 'J3Fh2ruKQcxWWxqQRAnG7EXSHNjeUoRRQtCUE4txpump',
  marketCap: '$0.00',
  hasImage: true
}
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'عملة ميمز عربي',
  symbol: 'ميم',
  mint: 'B5Vkz1tZA2YXCraDA87jF2rWYHgmvzXHBqCao9thpump',
  marketCap: '$0.00',
  hasImage: true
}
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'chickenBIGMAC',
  symbol: 'CBM',
  mint: 'EFoWKeNLK6rUT5VFRczea5yFosmjrGpypLmFuQgbpump',
  marketCap: '$0.00',
  hasImage: true
}
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'chickenBIGMAC',
  symbol: 'CBM',
  mint: 'EFoWKeNLK6rUT5VFRczea5yFosmjrGpypLmFuQgbpump',
  marketCap: '$0.00',
  hasImage: true
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: "Assassin's Creed",
  symbol: 'Creed',
  mint: '83ZoVxa3A39H39Ce6hZspDG5ZQ3gW8QmSkhpNqLUpump',
  marketCap: '$885.51',
  hasImage: true
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: "Assassin's Creed",
  symbol: 'Creed',
  mint: '83ZoVxa3A39H39Ce6hZspDG5ZQ3gW8QmSkhpNqLUpump',
  marketCap: '$885.51',
  hasImage: true
}
Updating metrics for 8 tokens
Invalid bonding curve account
Updated metrics for 83ZoVxa3A39H39Ce6hZspDG5ZQ3gW8QmSkhpNqLUpump in memory cache
Updated Creed: $1156.83 MC
Updated metrics for 83ZoVxa3A39H39Ce6hZspDG5ZQ3gW8QmSkhpNqLUpump in memory cache
Updated Creed: $1156.83 MC
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'waggy',
  symbol: 'WAG',
  mint: '8JyVmZGKGtwWbwga6NHZwdSmDMexV8m47M4rV3gApump',
  marketCap: '$0.00',
  hasImage: true
}
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'waggy',
  symbol: 'WAG',
  mint: '8JyVmZGKGtwWbwga6NHZwdSmDMexV8m47M4rV3gApump',
  marketCap: '$0.00',
  hasImage: true
}
Invalid bonding curve account
Invalid bonding curve account
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:47)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:108:20)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price: price = virtualSolReserves / virtualTokenReserves
  67 |     // This gives us the price of 1 token (with decimals) in lamports
> 68 |     const priceInLamports = virtualSolReserves.toNumber() / virtualTokenReserves.toNumber();
     |                                               ^
  69 |     
  70 |     // Convert to price per whole token in SOL
  71 |     const pricePerToken = (priceInLamports * Math.pow(10, TOKEN_DECIMALS)) / LAMPORTS_PER_SOL;
 ✓ Compiled in 64ms
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Invalid bonding curve account
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:47)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:108:20)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price: price = virtualSolReserves / virtualTokenReserves
  67 |     // This gives us the price of 1 token (with decimals) in lamports
> 68 |     const priceInLamports = virtualSolReserves.toNumber() / virtualTokenReserves.toNumber();
     |                                               ^
  69 |     
  70 |     // Convert to price per whole token in SOL
  71 |     const pricePerToken = (priceInLamports * Math.pow(10, TOKEN_DECIMALS)) / LAMPORTS_PER_SOL;
Error fetching metadata from https://ipfs.io/ipfs/bafkreidn6gmqmvj4fgwpzieqmjjf5fg3kt6pyi63cbvd7wreltlapoazbi : Error [AbortError]: This operation was aborted
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:139:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:43)
  137 |     
  138 |     try {
> 139 |       const response = await fetch(httpUri, { 
      |                       ^
  140 |         signal: controller.signal,
  141 |         headers: {
  142 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  code: 20,
  INDEX_SIZE_ERR: 1,
  DOMSTRING_SIZE_ERR: 2,
  HIERARCHY_REQUEST_ERR: 3,
  WRONG_DOCUMENT_ERR: 4,
  INVALID_CHARACTER_ERR: 5,
  NO_DATA_ALLOWED_ERR: 6,
  NO_MODIFICATION_ALLOWED_ERR: 7,
  NOT_FOUND_ERR: 8,
  NOT_SUPPORTED_ERR: 9,
  INUSE_ATTRIBUTE_ERR: 10,
  INVALID_STATE_ERR: 11,
  SYNTAX_ERR: 12,
  INVALID_MODIFICATION_ERR: 13,
  NAMESPACE_ERR: 14,
  INVALID_ACCESS_ERR: 15,
  VALIDATION_ERR: 16,
  TYPE_MISMATCH_ERR: 17,
  SECURITY_ERR: 18,
  NETWORK_ERR: 19,
  ABORT_ERR: 20,
  URL_MISMATCH_ERR: 21,
  QUOTA_EXCEEDED_ERR: 22,
  TIMEOUT_ERR: 23,
  INVALID_NODE_TYPE_ERR: 24,
  DATA_CLONE_ERR: 25
}
Error fetching metadata from https://ipfs.io/ipfs/bafkreidn6gmqmvj4fgwpzieqmjjf5fg3kt6pyi63cbvd7wreltlapoazbi : Error [AbortError]: This operation was aborted
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:139:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:43)
  137 |     
  138 |     try {
> 139 |       const response = await fetch(httpUri, { 
      |                       ^
  140 |         signal: controller.signal,
  141 |         headers: {
  142 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  code: 20,
  INDEX_SIZE_ERR: 1,
  DOMSTRING_SIZE_ERR: 2,
  HIERARCHY_REQUEST_ERR: 3,
  WRONG_DOCUMENT_ERR: 4,
  INVALID_CHARACTER_ERR: 5,
  NO_DATA_ALLOWED_ERR: 6,
  NO_MODIFICATION_ALLOWED_ERR: 7,
  NOT_FOUND_ERR: 8,
  NOT_SUPPORTED_ERR: 9,
  INUSE_ATTRIBUTE_ERR: 10,
  INVALID_STATE_ERR: 11,
  SYNTAX_ERR: 12,
  INVALID_MODIFICATION_ERR: 13,
  NAMESPACE_ERR: 14,
  INVALID_ACCESS_ERR: 15,
  VALIDATION_ERR: 16,
  TYPE_MISMATCH_ERR: 17,
  SECURITY_ERR: 18,
  NETWORK_ERR: 19,
  ABORT_ERR: 20,
  URL_MISMATCH_ERR: 21,
  QUOTA_EXCEEDED_ERR: 22,
  TIMEOUT_ERR: 23,
  INVALID_NODE_TYPE_ERR: 24,
  DATA_CLONE_ERR: 25
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Useless Shitty Bonk',
  symbol: 'USB',
  mint: '6S6GH6jNipcf7MoLWaqQgUHuqHJf86LMiP5dJTdupump',
  marketCap: '$1323.78',
  hasImage: false
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Useless Shitty Bonk',
  symbol: 'USB',
  mint: '6S6GH6jNipcf7MoLWaqQgUHuqHJf86LMiP5dJTdupump',
  marketCap: '$1323.78',
  hasImage: false
}
Finished updating token metrics
Updating metrics for 8 tokens
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'DROWSYLEAKED',
  symbol: 'DROWSYLEAK',
  mint: '9xeVAJrMrwEFjdMA3tfF237R3ayFYCjv57a7Trf9pump',
  marketCap: '$881.79',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error reading bonding curve: Error: failed to get info about account 9Dc17be3Tpw1Tc4stoUXRpxL1wbwY7NjLBdMNd21xgWC: Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}
    at async getPumpFunCurveData (src/lib/pumpfun-curve.ts:36:24)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:108:20)
    at async eval (src/app/api/pump-monitor/route.ts:323:44)
  34 | } | null> {
  35 |   try {
> 36 |     const accountInfo = await connection.getAccountInfo(
     |                        ^
  37 |       new PublicKey(bondingCurveAddress)
  38 |     );
  39 |     
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'DROWSYLEAKED',
  symbol: 'DROWSYLEAK',
  mint: '9xeVAJrMrwEFjdMA3tfF237R3ayFYCjv57a7Trf9pump',
  marketCap: '$0.00',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated metrics for 6S6GH6jNipcf7MoLWaqQgUHuqHJf86LMiP5dJTdupump in memory cache
Updated USB: $2017.07 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for 6S6GH6jNipcf7MoLWaqQgUHuqHJf86LMiP5dJTdupump in memory cache
Updated USB: $2017.07 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:47)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:108:20)
    at async eval (src/app/api/pump-monitor/route.ts:323:44)
  66 |     // Calculate price: price = virtualSolReserves / virtualTokenReserves
  67 |     // This gives us the price of 1 token (with decimals) in lamports
> 68 |     const priceInLamports = virtualSolReserves.toNumber() / virtualTokenReserves.toNumber();
     |                                               ^
  69 |     
  70 |     // Convert to price per whole token in SOL
  71 |     const pricePerToken = (priceInLamports * Math.pow(10, TOKEN_DECIMALS)) / LAMPORTS_PER_SOL;
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'poop corn',
  symbol: 'corn',
  mint: '7N36NZRDreXyrJ4ot2ztADmdadqHpGmp5wPCH6sSpump',
  marketCap: '$0.00',
  hasImage: true
}
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:47)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:108:20)
    at async eval (src/app/api/pump-monitor/route.ts:323:44)
  66 |     // Calculate price: price = virtualSolReserves / virtualTokenReserves
  67 |     // This gives us the price of 1 token (with decimals) in lamports
> 68 |     const priceInLamports = virtualSolReserves.toNumber() / virtualTokenReserves.toNumber();
     |                                               ^
  69 |     
  70 |     // Convert to price per whole token in SOL
  71 |     const pricePerToken = (priceInLamports * Math.pow(10, TOKEN_DECIMALS)) / LAMPORTS_PER_SOL;
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'poop corn',
  symbol: 'corn',
  mint: '7N36NZRDreXyrJ4ot2ztADmdadqHpGmp5wPCH6sSpump',
  marketCap: '$0.00',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Useless Shitty Bonk',
  symbol: 'USB',
  mint: '7usLyXwc8MJkmNApfsLaxFA9Tyqhyj9UaGD2C2ypump',
  marketCap: '$0.00',
  hasImage: true
}
 GET /api/pump-monitor 200 in 147926ms
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error fetching transaction: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated Creed: $953.31 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
 ○ Compiling / ...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
 ✓ Compiled / in 2.8s (704 modules)
 GET / 200 in 3106ms
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated Creed: $953.31 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'toast wif butter',
  symbol: 'toast',
  mint: '3zzLMZMzJTkAEcm5iHrTDmeU2cJWPXCVMjjFvD53pump',
  marketCap: '$5549.13',
  hasImage: true
}
Update already in progress, skipping...
 ✓ Compiled /api/pump-monitor in 349ms (1031 modules)
Starting background updater for token metrics
No tokens in cache to update
Redis Client Error ECONNREFUSED
Running without Redis cache - tokens will not persist
Redis Client Error ECONNREFUSED
Redis Client Error ECONNREFUSED
Failed to connect to Redis: [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Invalid bonding curve account
Invalid bonding curve account
Error fetching metadata from https://cloudflare-ipfs.com/ipfs/QmNzj9ZeFQQuphDWunphHBeXqxQq9R59bEaqmQhMmAGQbM : TypeError: fetch failed
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:139:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:43)
  137 |     
  138 |     try {
> 139 |       const response = await fetch(httpUri, { 
      |                       ^
  140 |         signal: controller.signal,
  141 |         headers: {
  142 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  [cause]: [Error: getaddrinfo ENOTFOUND cloudflare-ipfs.com] {
    errno: -3007,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'cloudflare-ipfs.com'
  }
}
Error fetching metadata from https://cloudflare-ipfs.com/ipfs/QmNzj9ZeFQQuphDWunphHBeXqxQq9R59bEaqmQhMmAGQbM : TypeError: fetch failed
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:139:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:43)
  137 |     
  138 |     try {
> 139 |       const response = await fetch(httpUri, { 
      |                       ^
  140 |         signal: controller.signal,
  141 |         headers: {
  142 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  [cause]: [Error: getaddrinfo ENOTFOUND cloudflare-ipfs.com] {
    errno: -3007,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'cloudflare-ipfs.com'
  }
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'WimpAura',
  symbol: 'WIMP',
  mint: '6W1GM36NxMYDbnbyeqoDHvPTvUZ9pex9WF6cXUhkfonP',
  marketCap: '$-************.58',
  hasImage: false
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/app/api/pump-monitor/route.ts:323:44)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'WimpAura',
  symbol: 'WIMP',
  mint: '6W1GM36NxMYDbnbyeqoDHvPTvUZ9pex9WF6cXUhkfonP',
  marketCap: '$0.00',
  hasImage: false
}
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Finished updating token metrics
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/app/api/pump-monitor/route.ts:323:44)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'womp',
  symbol: 'womp ',
  mint: 'FYq9ZQaFV1iSDV9wfXzsZj4FcRvdhT3VrQSYyoT4pump',
  marketCap: '$0.00',
  hasImage: true
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'womp',
  symbol: 'womp ',
  mint: 'FYq9ZQaFV1iSDV9wfXzsZj4FcRvdhT3VrQSYyoT4pump',
  marketCap: '$-676045032415.10',
  hasImage: true
}
Updating metrics for 10 tokens
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Updating metrics for 2 tokens
Updated metrics for 6W1GM36NxMYDbnbyeqoDHvPTvUZ9pex9WF6cXUhkfonP in memory cache
Updated WIMP: $-*************.32 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Earbnb',
  symbol: 'EARBNB',
  mint: 'ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump',
  marketCap: '$1185.90',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Earbnb',
  symbol: 'EARBNB',
  mint: 'ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump',
  marketCap: '$1185.90',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updated metrics for FYq9ZQaFV1iSDV9wfXzsZj4FcRvdhT3VrQSYyoT4pump in memory cache
Updated womp : $-1030292629400.61 MC
Finished updating token metrics
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for 3zzLMZMzJTkAEcm5iHrTDmeU2cJWPXCVMjjFvD53pump in memory cache
Updated toast: $2009.44 MC
Invalid bonding curve account
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Nico Coin',
  symbol: 'N1CO',
  mint: '5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump',
  marketCap: '$1031.08',
  hasImage: true
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Nico Coin',
  symbol: 'N1CO',
  mint: '5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump',
  marketCap: '$1031.08',
  hasImage: true
}
Updated metrics for 9xeVAJrMrwEFjdMA3tfF237R3ayFYCjv57a7Trf9pump in memory cache
Updated DROWSYLEAK: $884.98 MC
Updated metrics for 9xeVAJrMrwEFjdMA3tfF237R3ayFYCjv57a7Trf9pump in memory cache
Updated DROWSYLEAK: $884.98 MC
Update already in progress, skipping...
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Torilla Chip',
  symbol: 'Chip',
  mint: '9wkoc6h8ZVR9XayUo5sUSwgo86n8Z2nCCrWkvYdMpump',
  marketCap: '$0.00',
  hasImage: true
}
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Torilla Chip',
  symbol: 'Chip',
  mint: '9wkoc6h8ZVR9XayUo5sUSwgo86n8Z2nCCrWkvYdMpump',
  marketCap: '$0.00',
  hasImage: true
}
 GET /api/pump-monitor 200 in 120143ms
Updating metrics for 5 tokens
Updated USB: $892.20 MC
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for 5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump in memory cache
Updated N1CO: $676.70 MC
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Useless HOE coin',
  symbol: 'UHC',
  mint: '5PL5rcWjvYB2DHWAhC7XEmquwymhFnCFjVtHJdeSpump',
  marketCap: '$734.25',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump in memory cache
Updated EARBNB: $771.06 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated USB: $892.20 MC
Finished updating token metrics
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'why so',
  symbol: 'extra',
  mint: '95KtsNBMxCEQ2gvrXM8EAgKuXyVyu9a734iq5hxMpump',
  marketCap: '$763.85',
  hasImage: true
}
Updated metrics for 6W1GM36NxMYDbnbyeqoDHvPTvUZ9pex9WF6cXUhkfonP in memory cache
Updated WIMP: $-************.58 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for FYq9ZQaFV1iSDV9wfXzsZj4FcRvdhT3VrQSYyoT4pump in memory cache
Updated womp : $-676045032415.10 MC
Finished updating token metrics
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/app/api/pump-monitor/route.ts:323:44)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'MMAOTAI',
  symbol: 'mt',
  mint: 'EaJtGjXenCJNMughMqzoh57s8LaQn7nXnWMysBWiPUMp',
  marketCap: '$0.00',
  hasImage: true
}
 GET / 200 in 153ms
 GET /api/pump-monitor 200 in 356512ms
 ○ Compiling /favicon.ico ...
 ✓ Compiled /favicon.ico in 765ms (396 modules)
 GET /favicon.ico 200 in 879ms
 ✓ Compiled /api/pump-monitor in 355ms (1045 modules)
Starting background updater for token metrics
Redis Client Error ECONNREFUSED
Running without Redis cache - tokens will not persist
Redis Client Error ECONNREFUSED
Redis Client Error ECONNREFUSED
No tokens in cache to update
Failed to connect to Redis: [AggregateError: ] { code: 'ECONNREFUSED' }
Running without Redis cache - tokens will not persist
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'TICKET',
  symbol: 'TICKET',
  mint: 'JB3pugPusgWxrcYNyhX2sVmaeCv4yUpG4FSimi8Cpump',
  marketCap: '$578.90',
  hasImage: true
}
Updating metrics for 10 tokens
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Updating metrics for 5 tokens
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for 5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump in memory cache
Updated N1CO: $1009.00 MC
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Banger Bonk ',
  symbol: 'BANGER',
  mint: 'J62L5paopDwEpguuEFoTXF6CY72rduA8aLAx2XMspump',
  marketCap: '$0.00',
  hasImage: true
}
Updated metrics for ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump in memory cache
Updated EARBNB: $1174.63 MC
Updated metrics for 95KtsNBMxCEQ2gvrXM8EAgKuXyVyu9a734iq5hxMpump in memory cache
Updated extra: $580.97 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for 5PL5rcWjvYB2DHWAhC7XEmquwymhFnCFjVtHJdeSpump in memory cache
Updated UHC: $581.51 MC
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Error fetching metadata from https://cloudflare-ipfs.com/ipfs/QmPPfvFBAfmEFL1uonmiAienuMNnLoFRVC2s7mXV4TuTb2 : TypeError: fetch failed
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:139:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:43)
  137 |     
  138 |     try {
> 139 |       const response = await fetch(httpUri, { 
      |                       ^
  140 |         signal: controller.signal,
  141 |         headers: {
  142 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  [cause]: [Error: getaddrinfo ENOTFOUND cloudflare-ipfs.com] {
    errno: -3007,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'cloudflare-ipfs.com'
  }
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Jo Smurft',
  symbol: 'JO',
  mint: 'FfdbewELK9WvUT4uXyXHQ2kvueXjpaf8UR4QRYscW1Pb',
  marketCap: '$-1041546809837.51',
  hasImage: false
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for 5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump in memory cache
Updated N1CO: $662.33 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump in memory cache
Updated EARBNB: $697.45 MC
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updating metrics for 3 tokens
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error fetching transaction: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated metrics for 6W1GM36NxMYDbnbyeqoDHvPTvUZ9pex9WF6cXUhkfonP in memory cache
Updated WIMP: $-1041546809837.51 MC
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for FfdbewELK9WvUT4uXyXHQ2kvueXjpaf8UR4QRYscW1Pb in memory cache
Updated JO: $-1041546809837.51 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error fetching transaction: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for JB3pugPusgWxrcYNyhX2sVmaeCv4yUpG4FSimi8Cpump in memory cache
Updated TICKET: $887.36 MC
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'China’s Space Solar Plan',
  symbol: 'CSSP',
  mint: 'GNYdj1xk7r8YeFn9r56KC9RveVkqcNqDQ7r8XhmczsnA',
  marketCap: '$-*************.91',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
 GET / 200 in 115ms
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
 GET / 200 in 56ms
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for FYq9ZQaFV1iSDV9wfXzsZj4FcRvdhT3VrQSYyoT4pump in memory cache
Updated womp : $-1029887002381.17 MC
Finished updating token metrics
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Elon Musk Reborn ',
  symbol: '(EMRB)',
  mint: 'FK7PBmcuNRUDrJsv25z4kvzWS9j8AZC6UKNhuHUapump',
  marketCap: '$903.19',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated metrics for 3zzLMZMzJTkAEcm5iHrTDmeU2cJWPXCVMjjFvD53pump in memory cache
Updated toast: $1208.32 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Finished updating token metrics
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Finished updating token metrics
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Pixels Pixels Lovely Pixels',
  symbol: 'PIXELS',
  mint: '3ZB9mNYrrnuLad1QX3t5q8LCdE5nNiy2uYcej3PsvJhP',
  marketCap: '$-*************.91',
  hasImage: true
}
Updating metrics for 6 tokens
Updated metrics for 3ZB9mNYrrnuLad1QX3t5q8LCdE5nNiy2uYcej3PsvJhP in memory cache
Updated PIXELS: $-*************.91 MC
Updated metrics for FK7PBmcuNRUDrJsv25z4kvzWS9j8AZC6UKNhuHUapump in memory cache
Updated (EMRB): $909.45 MC
Updated metrics for GNYdj1xk7r8YeFn9r56KC9RveVkqcNqDQ7r8XhmczsnA in memory cache
Updated CSSP: $-*************.91 MC
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Buildrs',
  symbol: 'Buildrs',
  mint: 'HqMhwTtafbr9JQjCYXKDarutYzKBSQdWZE26QVjcpump',
  marketCap: '$1515.47',
  hasImage: true
}
Updating metrics for 10 tokens
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Invalid bonding curve account
Updated metrics for FfdbewELK9WvUT4uXyXHQ2kvueXjpaf8UR4QRYscW1Pb in memory cache
Updated JO: $-*************.91 MC
Updated metrics for JB3pugPusgWxrcYNyhX2sVmaeCv4yUpG4FSimi8Cpump in memory cache
Updated TICKET: $885.44 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updating metrics for 5 tokens
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updated metrics for 5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump in memory cache
Updated N1CO: $676.51 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error fetching metadata from https://cloudflare-ipfs.com/ipfs/Qmd4GjeiwBSk7JnDoGXmftU2cHTHibNcJNE8Dc8KLYc5Pb : TypeError: fetch failed
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:139:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:43)
  137 |     
  138 |     try {
> 139 |       const response = await fetch(httpUri, { 
      |                       ^
  140 |         signal: controller.signal,
  141 |         headers: {
  142 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  [cause]: [Error: getaddrinfo ENOTFOUND cloudflare-ipfs.com] {
    errno: -3007,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'cloudflare-ipfs.com'
  }
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'TeslaCoin',
  symbol: 'TESLA',
  mint: '5TXN6vC4BXanVbPwpSjpgxmyAmvqkbtJb62VSJgtvYyQ',
  marketCap: '$-************.58',
  hasImage: false
}
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'PumpDoge',
  symbol: 'PumpDoge',
  mint: 'FNQ1ZiBTqbALLTC7hNDc13pp8GLH8ZYWw9hMtXsppump',
  marketCap: '$-1484127019654.08',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error fetching transaction: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated metrics for ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump in memory cache
Updated EARBNB: $619.38 MC
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'poop dog',
  symbol: 'poopdog',
  mint: '8KatRz4Zp3B698SMgoR5qJfWN24dHgKD5j1A2etVpump',
  marketCap: '$844.15',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updated metrics for 5PL5rcWjvYB2DHWAhC7XEmquwymhFnCFjVtHJdeSpump in memory cache
Updated UHC: $633.60 MC
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updated metrics for 95KtsNBMxCEQ2gvrXM8EAgKuXyVyu9a734iq5hxMpump in memory cache
Updated extra: $1130.95 MC
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for 5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump in memory cache
Updated N1CO: $676.51 MC
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Finished updating token metrics
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump in memory cache
Updated EARBNB: $619.38 MC
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Release The Files',
  symbol: 'Epstein',
  mint: '8FMHGwg7ZHrVxM5qu3cZdBnhBesY9LTuECZqbfVpump',
  marketCap: '$3490.52',
  hasImage: true
}
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated metrics for FYq9ZQaFV1iSDV9wfXzsZj4FcRvdhT3VrQSYyoT4pump in memory cache
Updated womp : $-676045032415.10 MC
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error fetching transaction: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for 3zzLMZMzJTkAEcm5iHrTDmeU2cJWPXCVMjjFvD53pump in memory cache
Updated toast: $716.12 MC
Updated metrics for 6W1GM36NxMYDbnbyeqoDHvPTvUZ9pex9WF6cXUhkfonP in memory cache
Updated WIMP: $-************.58 MC
Finished updating token metrics
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'i cant live without this',
  symbol: 'solmate',
  mint: '47VHMTPkudBvoxY3hdCE37Vd34sLHm1nRjx6TohPpump',
  marketCap: '$1211.51',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Finished updating token metrics
Updating metrics for 10 tokens
Error fetching metadata from https://ipfs.io/ipfs/bafkreihavnwjphse2ws3has7b6awg7ndtdxxdtb3nczobcf2wgsvwvtktu : Error [AbortError]: This operation was aborted
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:139:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:43)
  137 |     
  138 |     try {
> 139 |       const response = await fetch(httpUri, { 
      |                       ^
  140 |         signal: controller.signal,
  141 |         headers: {
  142 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  code: 20,
  INDEX_SIZE_ERR: 1,
  DOMSTRING_SIZE_ERR: 2,
  HIERARCHY_REQUEST_ERR: 3,
  WRONG_DOCUMENT_ERR: 4,
  INVALID_CHARACTER_ERR: 5,
  NO_DATA_ALLOWED_ERR: 6,
  NO_MODIFICATION_ALLOWED_ERR: 7,
  NOT_FOUND_ERR: 8,
  NOT_SUPPORTED_ERR: 9,
  INUSE_ATTRIBUTE_ERR: 10,
  INVALID_STATE_ERR: 11,
  SYNTAX_ERR: 12,
  INVALID_MODIFICATION_ERR: 13,
  NAMESPACE_ERR: 14,
  INVALID_ACCESS_ERR: 15,
  VALIDATION_ERR: 16,
  TYPE_MISMATCH_ERR: 17,
  SECURITY_ERR: 18,
  NETWORK_ERR: 19,
  ABORT_ERR: 20,
  URL_MISMATCH_ERR: 21,
  QUOTA_EXCEEDED_ERR: 22,
  TIMEOUT_ERR: 23,
  INVALID_NODE_TYPE_ERR: 24,
  DATA_CLONE_ERR: 25
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: '11:11',
  symbol: '11:11',
  mint: '6imBanRMQJrzESQLi1G1wPBJLnZ1BBJU8RWzDDhcpump',
  marketCap: '$903.33',
  hasImage: false
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Luckdotfun',
  symbol: 'Luck',
  mint: '4XnHDhtvUBNEgejouLSQKa4hfJUQvxsFooiy8BGopump',
  marketCap: '$1301.98',
  hasImage: true
}
Updating metrics for 10 tokens
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Updated metrics for 47VHMTPkudBvoxY3hdCE37Vd34sLHm1nRjx6TohPpump in memory cache
Updated solmate: $1271.44 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updating metrics for 5 tokens
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Luck',
  symbol: 'Luck',
  mint: '3rkh4JGz5ZzEUCHwC6GSKbce9FmChhFrP9iCRcGZpump',
  marketCap: '$930.81',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Alien Pacu Ufo ',
  symbol: 'ALPACU',
  mint: 'B2QsSUhkjZBKPoRb2EABv9W7RPjzHpYjVdjZ6FoApump',
  marketCap: '$630.81',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump in memory cache
Updated EARBNB: $619.38 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated metrics for 5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump in memory cache
Updated N1CO: $676.51 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updated metrics for 6W1GM36NxMYDbnbyeqoDHvPTvUZ9pex9WF6cXUhkfonP in memory cache
Updated WIMP: $-************.58 MC
Updated metrics for 8FMHGwg7ZHrVxM5qu3cZdBnhBesY9LTuECZqbfVpump in memory cache
Updated Epstein: $1905.49 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error reading bonding curve: Error: failed to get info about account 3U9xp9WR3LNqsiaF8vWpERjcyG3c3ewqzMveywwquD9A: Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}
    at async getPumpFunCurveData (src/lib/pumpfun-curve.ts:36:24)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:113:20)
    at async eval (src/app/api/pump-monitor/route.ts:323:44)
  34 | } | null> {
  35 |   try {
> 36 |     const accountInfo = await connection.getAccountInfo(
     |                        ^
  37 |       new PublicKey(bondingCurveAddress)
  38 |     );
  39 |     
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Alien Elon Musk',
  symbol: 'EALIEN',
  mint: 'Fqux7uUeKRuCoGFMZhdYSvGk1rXyRS6kNdn14ikCpump',
  marketCap: '$0.00',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for 8KatRz4Zp3B698SMgoR5qJfWN24dHgKD5j1A2etVpump in memory cache
Updated poopdog: $1401.22 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error reading bonding curve: Error: failed to get info about account 5TXN6vC4BXanVbPwpSjpgxmyAmvqkbtJb62VSJgtvYyQ: Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}
    at async getPumpFunCurveData (src/lib/pumpfun-curve.ts:36:24)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:113:20)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  34 | } | null> {
  35 |   try {
> 36 |     const accountInfo = await connection.getAccountInfo(
     |                        ^
  37 |       new PublicKey(bondingCurveAddress)
  38 |     );
  39 |     
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for 5PL5rcWjvYB2DHWAhC7XEmquwymhFnCFjVtHJdeSpump in memory cache
Updated UHC: $965.29 MC
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated metrics for FNQ1ZiBTqbALLTC7hNDc13pp8GLH8ZYWw9hMtXsppump in memory cache
Updated PumpDoge: $-2261067514442.99 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Sharkboy',
  symbol: 'Sharkboy',
  mint: '5regPK32GapbcQkPWxDt5gxYDaHzAYZEexq9R1ACpump',
  marketCap: '$882.75',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for 95KtsNBMxCEQ2gvrXM8EAgKuXyVyu9a734iq5hxMpump in memory cache
Updated extra: $1734.57 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'POLERO',
  symbol: 'POLERO',
  mint: '4jwWvNQezzAJjS1CEPvqtep3dibsBBx7QFJLxF7Jpump',
  marketCap: '$1417.40',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated metrics for FYq9ZQaFV1iSDV9wfXzsZj4FcRvdhT3VrQSYyoT4pump in memory cache
Updated womp : $-676045032415.10 MC
Finished updating token metrics
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'CHAZZZ COIN',
  symbol: 'CHAZZZ',
  mint: 'CUyUUVQd6sKyvQVBV6gqgkxK1eo7qrnaaU4aQFqpump',
  marketCap: '$1718.27',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error fetching transaction: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Error reading bonding curve: Error: failed to get info about account H3ebkR3sm4nVXmrVzXA9CJ1KoJ8NNmL7uLa9RvGWSBrJ: Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}
    at async getPumpFunCurveData (src/lib/pumpfun-curve.ts:36:24)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  34 | } | null> {
  35 |   try {
> 36 |     const accountInfo = await connection.getAccountInfo(
     |                        ^
  37 |       new PublicKey(bondingCurveAddress)
  38 |     );
  39 |     
Updated metrics for 5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump in memory cache
Updated N1CO: $1030.67 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'escape wage slavery with',
  symbol: 'WAGECOIN',
  mint: 'Ey87tHhbWnNHYdkQ8RZKAQkq4vuciJzsrA4tA19spump',
  marketCap: '$1096.83',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated Buildrs: $886.62 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated PIXELS: $-1041615179721.31 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated (EMRB): $886.75 MC
Updated CSSP: $-1041615179721.31 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated JO: $-1041615179721.31 MC
Finished updating token metrics
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'kunz',
  symbol: 'kunz',
  mint: '7GYpPupfVMtMaKBYwpWXUWBybULbFWAR8UjcYiFtpump',
  marketCap: '$1164.11',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'EGG BOY',
  symbol: 'EGGBOY',
  mint: 'HPfRjYD6KDiPtn5wY2T2n5RJfvtRgny2bbFJwSKVNfd8',
  marketCap: '$-*************.32',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updating metrics for 10 tokens
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updating metrics for 5 tokens
Updated metrics for 3zzLMZMzJTkAEcm5iHrTDmeU2cJWPXCVMjjFvD53pump in memory cache
Updated toast: $884.14 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Invalid bonding curve account
Error reading bonding curve: Error: failed to get info about account 5rVyf6sMyhrBroQVpV85BkYhV1vdKyK3NzY2KVK6dQX6: Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}
    at async getPumpFunCurveData (src/lib/pumpfun-curve.ts:36:24)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:113:20)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  34 | } | null> {
  35 |   try {
> 36 |     const accountInfo = await connection.getAccountInfo(
     |                        ^
  37 |       new PublicKey(bondingCurveAddress)
  38 |     );
  39 |     
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Invalid bonding curve account
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Bin Chicken Token',
  symbol: 'BCT',
  mint: 'BKjDKK3RSbfZwB5s9A6dMax7roUgRKcTJX33v6mRpump',
  marketCap: '$0.00',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated metrics for 5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump in memory cache
Updated N1CO: $676.51 MC
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated metrics for HPfRjYD6KDiPtn5wY2T2n5RJfvtRgny2bbFJwSKVNfd8 in memory cache
Updated EGGBOY: $-*************.32 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'RugPull',
  symbol: 'RGPLL',
  mint: '43MtWqob1N1ups5qjDbzVgVtArxsPXYgd5dkhNfMpump',
  marketCap: '$718.05',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Finished updating token metrics
Updated metrics for ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump in memory cache
Updated EARBNB: $619.38 MC
Updated metrics for 7GYpPupfVMtMaKBYwpWXUWBybULbFWAR8UjcYiFtpump in memory cache
Updated kunz: $1096.61 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Updated metrics for 6W1GM36NxMYDbnbyeqoDHvPTvUZ9pex9WF6cXUhkfonP in memory cache
Updated WIMP: $-************.58 MC
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for FYq9ZQaFV1iSDV9wfXzsZj4FcRvdhT3VrQSYyoT4pump in memory cache
Updated womp : $-676045032415.10 MC
Finished updating token metrics
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updated metrics for CUyUUVQd6sKyvQVBV6gqgkxK1eo7qrnaaU4aQFqpump in memory cache
Updated CHAZZZ: $926.41 MC
Update already in progress, skipping...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Anti American Tariff Hat',
  symbol: 'AATHAT',
  mint: '2GJujZHftDWwp6i5Xj1LK7xnojqnF7VENqWRbVHgi7Ma',
  marketCap: '$-************.58',
  hasImage: true
}
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Secret to wealth',
  symbol: 'Wealth',
  mint: 'DPRm4Vr62g7AnWQ6KuTiaLdriYooVc4MxwiFGUxppump',
  marketCap: '$885.65',
  hasImage: true
}
Updating metrics for 10 tokens
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error reading bonding curve: Error: Number can only safely store up to 53 bits
    at getPumpFunCurveData (src/lib/pumpfun-curve.ts:68:35)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  66 |     // Calculate price using BN arithmetic to avoid overflow
  67 |     // Price = (virtualSolReserves * 10^decimals) / (virtualTokenReserves * 10^9)
> 68 |     const decimalsMultiplier = new BN(10).pow(new BN(TOKEN_DECIMALS));
     |                                   ^
  69 |     const lamportsMultiplier = new BN(LAMPORTS_PER_SOL);
  70 |     
  71 |     // Calculate price in SOL per token
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updating metrics for 5 tokens
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'Fake Bonk',
  symbol: 'FONK',
  mint: '5MfhjjjEEq7CJPQJ13XxqFVHBP32gEuUpHU6iPZ8pump',
  marketCap: '$1596.18',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
 ✓ Compiled in 956ms (704 modules)
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
 GET / 200 in 221ms
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated metrics for 5wtZhkC8834kwynCKSVungrnY157D6NNLmbtHvLApump in memory cache
Updated N1CO: $1030.94 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated metrics for 4jwWvNQezzAJjS1CEPvqtep3dibsBBx7QFJLxF7Jpump in memory cache
Updated POLERO: $3796.45 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Error fetching metadata from https://cloudflare-ipfs.com/ipfs/QmcxUgx4fJspEA7z2pyHR9XgXLdKqzBX6cBWv3GKkBFWC3 : TypeError: fetch failed
    at async fetchTokenMetadata (src/app/api/pump-monitor/route.ts:139:23)
    at async eval (src/app/api/pump-monitor/route.ts:299:43)
  137 |     
  138 |     try {
> 139 |       const response = await fetch(httpUri, { 
      |                       ^
  140 |         signal: controller.signal,
  141 |         headers: {
  142 |           'User-Agent': 'Mozilla/5.0 (compatible; PumpFun Monitor/1.0)' {
  [cause]: [Error: getaddrinfo ENOTFOUND cloudflare-ipfs.com] {
    errno: -3007,
    code: 'ENOTFOUND',
    syscall: 'getaddrinfo',
    hostname: 'cloudflare-ipfs.com'
  }
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
 ✓ Compiled in 297ms (324 modules)
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
 GET / 200 in 28ms
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'CICI Foreman',
  symbol: 'CICI',
  mint: '7wfvh5TiMKXx61sWjRtKhEdBAPZfCqnwqWDiD9pjirca',
  marketCap: '$-1042093768907.92',
  hasImage: false
}
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'poopcat ',
  symbol: 'POOPCAT',
  mint: 'J38HXLroxUMN6hipVZXA3A2tCKoU9RGapknySiZnpump',
  marketCap: '$1164.26',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'uko',
  symbol: 'uko ',
  mint: 'FJnWskTDvYFAefiBpxyv9DtvXCq9PMxBs37FJh1pump',
  marketCap: '$2369.74',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Updated Sharkboy: $1136.78 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
 ✓ Compiled in 175ms (324 modules)
 GET / 200 in 48ms
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updated metrics for 95KtsNBMxCEQ2gvrXM8EAgKuXyVyu9a734iq5hxMpump in memory cache
Updated extra: $1519.55 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated EALIEN: $26821.07 MC
Error reading bonding curve: Error: failed to get info about account GFEVU8ewy5RrsgNQ8X5t2S5ppPGYEf3y3dDgFeXi4Tw8: Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}
    at async getPumpFunCurveData (src/lib/pumpfun-curve.ts:36:24)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:113:20)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  34 | } | null> {
  35 |   try {
> 36 |     const accountInfo = await connection.getAccountInfo(
     |                        ^
  37 |       new PublicKey(bondingCurveAddress)
  38 |     );
  39 |     
Updated metrics for ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump in memory cache
Updated EARBNB: $943.88 MC
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
 ✓ Compiled in 186ms (324 modules)
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
 GET / 200 in 19ms
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Updated metrics for 5PL5rcWjvYB2DHWAhC7XEmquwymhFnCFjVtHJdeSpump in memory cache
Updated UHC: $581.92 MC
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Update already in progress, skipping...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Invalid bonding curve account
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'See Without A Camera',
  symbol: 'SEE',
  mint: '3UKtqmjy13MEnxJFCLJRk12Wyab8XJq4hSqE3QuZTkSs',
  marketCap: '$-1042093768907.92',
  hasImage: true
}
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
 ✓ Compiled in 189ms (324 modules)
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
 GET / 200 in 31ms
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Error reading bonding curve: Error: failed to get info about account 9Hjdw9oC5ZWZLjz5HpddC3zDbJucCaFLqdYQcVJQpdaq: Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}
    at async getPumpFunCurveData (src/lib/pumpfun-curve.ts:36:24)
    at async calculatePumpFunMetrics (src/lib/pumpfun-curve.ts:97:2)
    at async eval (src/lib/background-updater.ts:97:40)
    at async updateAllTokenMetrics (src/lib/background-updater.ts:74:6)
  34 | } | null> {
  35 |   try {
> 36 |     const accountInfo = await connection.getAccountInfo(
     |                        ^
  37 |       new PublicKey(bondingCurveAddress)
  38 |     );
  39 |     
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Updated Luck: $1399.51 MC
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error getting token activity: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Updated metrics for 6W1GM36NxMYDbnbyeqoDHvPTvUZ9pex9WF6cXUhkfonP in memory cache
Updated WIMP: $-1041888659256.52 MC
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
 ✓ Compiled in 218ms (324 modules)
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
 GET / 200 in 27ms
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Token added to memory cache (Redis unavailable)
New PumpFun token detected and cached: {
  name: 'locked',
  symbol: 'LOCKED',
  mint: 'HaZfZwwYWSqghajb1VgApG4YXvwVea5EyjM6yjH4Aqtx',
  marketCap: '$-1042093768907.92',
  hasImage: true
}
Updated metrics for ESDDesUFEExjreirsHdA1hwbkbadN8xiUkkW68Nypump in memory cache
Updated EARBNB: $619.38 MC
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Error fetching transaction: [Error: 429 Too Many Requests: {"jsonrpc":"2.0","error":{"code":-32429,"message":"rate limited"}}]
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 4000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 2000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 500ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
Server responded with 429 Too Many Requests.  Retrying after 1000ms delay...
[?25h
